# %% [markdown]
# # Complete ARCH Model with Ljung-Box Testing
# 
# This notebook provides a comprehensive ARCH model implementation including:
# 1. Data extraction from Yahoo Finance
# 2. Feature engineering for ARCH modeling
# 3. Automatic parameter selection
# 4. ARCH model training and testing
# 5. Comprehensive Ljung-Box tests for residuals
# 6. Results saving and visualization

# %%
import pandas as pd
import numpy as np
import yfinance as yf
import warnings
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import os

# Suppress warnings for cleaner output
warnings.filterwarnings('ignore')

# Install required packages if not available
try:
    from arch import arch_model
    print("✅ arch package available")
except ImportError:
    print("Installing arch package...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "arch"])
    from arch import arch_model

try:
    from sklearn.metrics import mean_squared_error, mean_absolute_error
    print("✅ sklearn available")
except ImportError:
    print("Installing scikit-learn...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "scikit-learn"])
    from sklearn.metrics import mean_squared_error, mean_absolute_error

try:
    from statsmodels.stats.diagnostic import acorr_ljungbox
    from statsmodels.tsa.stattools import adfuller
    print("✅ statsmodels available")
except ImportError:
    print("Installing statsmodels...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "statsmodels"])
    from statsmodels.stats.diagnostic import acorr_ljungbox
    from statsmodels.tsa.stattools import adfuller

print("🏛️ Complete ARCH Model with Ljung-Box Testing")
print("=" * 60)

# %% [markdown]
# ## 🎛️ ARCH MODEL PARAMETERS - MODIFY HERE!

# %%
# ============================================================================
# 🎛️ MODIFY THESE PARAMETERS FOR ARCH MODEL EXPERIMENTATION
# ============================================================================

# Data Parameters
DATA_YEARS = 5              # 🔧 CHANGE HERE: Years of data to fetch (1, 2, 3, 5)
TRAIN_RATIO = 0.8           # 🔧 CHANGE HERE: Training data ratio (0.7, 0.8, 0.9)
TARGET_HORIZON = 10         # 🔧 CHANGE HERE: Prediction horizon in days (5, 10, 15, 20)

# ARCH Model Parameters
AUTO_SELECT_Q = True        # 🔧 CHANGE HERE: Auto-select optimal q parameter (True/False)
MAX_Q = 5                   # 🔧 CHANGE HERE: Maximum q parameter to test (1-10)
MANUAL_Q = 1                # 🔧 CHANGE HERE: Manual q parameter (if AUTO_SELECT_Q = False)

# Ljung-Box Test Parameters
LJUNG_BOX_LAGS = [5, 10, 15, 20]  # 🔧 CHANGE HERE: Lag periods to test
SIGNIFICANCE_LEVEL = 0.05   # 🔧 CHANGE HERE: Significance level (0.01, 0.05, 0.10)

# Forecasting Parameters
FORECAST_METHOD = 'rolling'  # 🔧 CHANGE HERE: 'rolling', 'expanding', 'fixed'
ROLLING_WINDOW = 252         # 🔧 CHANGE HERE: Rolling window size (125, 252, 500)

# Output Parameters
SAVE_RESULTS = True          # 🔧 CHANGE HERE: Save results to files (True/False)
CREATE_EXCEL = True          # 🔧 CHANGE HERE: Create Excel files (True/False)
CREATE_PLOTS = True          # 🔧 CHANGE HERE: Create plots (True/False)
OUTPUT_FOLDER = 'arch_model_results'  # 🔧 CHANGE HERE: Output folder name

print(f"📊 ARCH Model Configuration:")
print(f"  - Data Years: {DATA_YEARS}")
print(f"  - Auto Select q: {AUTO_SELECT_Q}")
print(f"  - Max q to Test: {MAX_Q}")
print(f"  - Target Horizon: {TARGET_HORIZON} days")
print(f"  - Ljung-Box Lags: {LJUNG_BOX_LAGS}")
print(f"  - Significance Level: {SIGNIFICANCE_LEVEL}")

# ============================================================================

# %% [markdown]
# ## 1. Data Extraction from Yahoo Finance

# %%
def fetch_bitcoin_data(years=5):
    """Fetch Bitcoin data from Yahoo Finance for ARCH modeling"""
    print(f"\n📊 Fetching {years} years of Bitcoin data from Yahoo Finance...")
    
    end_date = datetime.now()
    start_date = end_date - timedelta(days=years*365 + 30)
    
    try:
        btc_ticker = "BTC-USD"
        btc = yf.Ticker(btc_ticker)
        btc_data = btc.history(start=start_date.strftime('%Y-%m-%d'), 
                              end=end_date.strftime('%Y-%m-%d'), 
                              interval='1d')
        
        if btc_data.empty:
            raise ValueError("No data retrieved from Yahoo Finance")
        
        # Clean and prepare data
        btc_data.columns = btc_data.columns.str.lower()
        btc_data = btc_data[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # Remove timezone if present
        if btc_data.index.tz is not None:
            btc_data.index = btc_data.index.tz_localize(None)
        
        # Remove any NaN values
        btc_data = btc_data.dropna()
        
        print(f"✅ Successfully fetched {len(btc_data)} days of Bitcoin data")
        print(f"📅 Date range: {btc_data.index.min().strftime('%Y-%m-%d')} to {btc_data.index.max().strftime('%Y-%m-%d')}")
        print(f"💰 Price range: ${btc_data['close'].min():.2f} to ${btc_data['close'].max():.2f}")
        
        return btc_data
        
    except Exception as e:
        print(f"❌ Error fetching Bitcoin data: {e}")
        return None

# Fetch Bitcoin data
btc_data = fetch_bitcoin_data(DATA_YEARS)
if btc_data is None:
    raise ValueError("Failed to fetch Bitcoin data")

# Display basic statistics
print(f"\n📈 Bitcoin Data Summary:")
print(f"  - Total observations: {len(btc_data)}")
print(f"  - Average daily return: {btc_data['close'].pct_change().mean()*100:.4f}%")
print(f"  - Daily volatility: {btc_data['close'].pct_change().std()*100:.4f}%")
print(f"  - Annualized volatility: {btc_data['close'].pct_change().std()*np.sqrt(252)*100:.2f}%")

# %% [markdown]
# ## 2. Feature Engineering for ARCH Model

# %%
class ARCHFeatureEngine:
    """Specialized feature engineering for ARCH volatility models"""
    
    def __init__(self, df):
        self.df = df.copy()
        self.df.columns = self.df.columns.str.lower()
        
    def calculate_arch_features(self):
        """Calculate features specifically needed for ARCH modeling"""
        print("\n🔧 Calculating ARCH-specific features...")
        
        close = self.df['close']
        high = self.df['high']
        low = self.df['low']
        
        # 1. Returns (core for ARCH)
        print("  - Calculating returns...")
        self.df['log_return_1d'] = np.log(close / close.shift(1))
        self.df['simple_return_1d'] = close.pct_change()
        self.df['squared_return'] = self.df['log_return_1d'] ** 2
        self.df['abs_return'] = abs(self.df['log_return_1d'])
        
        # 2. Historical volatility measures
        print("  - Calculating volatility measures...")
        returns = self.df['log_return_1d']
        for window in [5, 10, 20, 30, 60]:
            self.df[f'volatility_{window}d'] = returns.rolling(window).std() * np.sqrt(252)
        
        # 3. ARCH-specific features
        print("  - Calculating ARCH-specific indicators...")
        
        # Volatility clustering indicators
        self.df['vol_clustering_5d'] = self.df['squared_return'].rolling(5).mean()
        self.df['vol_clustering_10d'] = self.df['squared_return'].rolling(10).mean()
        
        # Return magnitude measures
        self.df['return_magnitude'] = self.df['abs_return']
        
        # Volatility regime indicators
        vol_20d = self.df['volatility_20d']
        vol_median = vol_20d.rolling(252).median()
        self.df['high_vol_regime'] = (vol_20d > vol_median * 1.5).astype(int)
        self.df['low_vol_regime'] = (vol_20d < vol_median * 0.5).astype(int)
        
        # ARCH effects indicators
        squared_returns = self.df['squared_return']
        self.df['arch_proxy_5d'] = squared_returns.rolling(5).mean()
        self.df['arch_proxy_10d'] = squared_returns.rolling(10).mean()
        
        # Extreme return indicators
        return_threshold = self.df['abs_return'].rolling(252).quantile(0.95)
        self.df['extreme_return_flag'] = (self.df['abs_return'] > return_threshold).astype(int)
        
        # 4. Target variables for different horizons
        print("  - Calculating target variables...")
        for period in [5, 10, 15, 20, 30]:
            # Future volatility (realized volatility)
            future_vol = returns.rolling(period).std().shift(-period) * np.sqrt(252)
            self.df[f'volatility_target_{period}d'] = future_vol
            
            # Future squared returns (alternative target)
            future_squared_return = self.df['squared_return'].rolling(period).mean().shift(-period)
            self.df[f'squared_return_target_{period}d'] = future_squared_return
        
        # 5. Return statistics for diagnostics
        print("  - Calculating return statistics...")
        self.df['return_skewness_20d'] = returns.rolling(20).apply(
            lambda x: stats.skew(x.dropna()) if len(x.dropna()) >= 3 else np.nan
        )
        self.df['return_kurtosis_20d'] = returns.rolling(20).apply(
            lambda x: stats.kurtosis(x.dropna()) if len(x.dropna()) >= 3 else np.nan
        )
        
        print("✅ ARCH feature engineering completed!")
        return self.df
    
    def check_arch_effects(self, returns, lags=10):
        """Check for ARCH effects in returns using Ljung-Box test on squared returns"""
        print(f"\n🔍 Checking for ARCH effects in raw returns...")
        
        squared_returns = returns ** 2
        
        # Ljung-Box test on squared returns
        lb_result = acorr_ljungbox(squared_returns.dropna(), lags=lags, return_df=True)
        
        print(f"📊 ARCH Effects Test (Ljung-Box on squared returns):")
        print(f"  - Test Statistic (lag {lags}): {lb_result['lb_stat'].iloc[-1]:.4f}")
        print(f"  - P-value (lag {lags}): {lb_result['lb_pvalue'].iloc[-1]:.6f}")
        
        if lb_result['lb_pvalue'].iloc[-1] < 0.05:
            print(f"  ✅ ARCH effects detected (p < 0.05) - ARCH model appropriate")
            return True
        else:
            print(f"  ⚠️ No significant ARCH effects detected (p >= 0.05)")
            return False
        
    def test_stationarity(self, series, name="Series"):
        """Test stationarity using Augmented Dickey-Fuller test"""
        print(f"\n📈 Testing stationarity of {name}...")
        
        # Remove NaN values
        clean_series = series.dropna()
        
        if len(clean_series) < 10:
            print(f"  ❌ Insufficient data for stationarity test")
            return False
        
        # ADF test
        adf_result = adfuller(clean_series)
        
        print(f"📊 Augmented Dickey-Fuller Test for {name}:")
        print(f"  - ADF Statistic: {adf_result[0]:.6f}")
        print(f"  - P-value: {adf_result[1]:.6f}")
        print(f"  - Critical Values: {adf_result[4]}")
        
        if adf_result[1] <= 0.05:
            print(f"  ✅ {name} is stationary (p <= 0.05)")
            return True
        else:
            print(f"  ⚠️ {name} may not be stationary (p > 0.05)")
            return False

# Apply feature engineering
feature_engine = ARCHFeatureEngine(btc_data)
featured_data = feature_engine.calculate_arch_features()

# Check for ARCH effects and stationarity
returns = featured_data['log_return_1d'].dropna()
arch_effects_present = feature_engine.check_arch_effects(returns, lags=10)
returns_stationary = feature_engine.test_stationarity(returns, "Log Returns")

print(f"\n📋 Data Preparation Summary:")
print(f"  - ARCH effects present: {'Yes' if arch_effects_present else 'No'}")
print(f"  - Returns stationary: {'Yes' if returns_stationary else 'No'}")
print(f"  - Ready for ARCH modeling: {'Yes' if arch_effects_present and returns_stationary else 'Check data'}")

# %% [markdown]
# ## 3. Create Output Folder

# %%
def create_output_folder(folder_name):
    """Create output folder if it doesn't exist"""
    if not os.path.exists(folder_name):
        os.makedirs(folder_name)
        print(f"📁 Created output folder: {folder_name}")
    else:
        print(f"📁 Using existing folder: {folder_name}")
    return folder_name

# Create output folder early
if SAVE_RESULTS or CREATE_EXCEL or CREATE_PLOTS:
    output_folder = create_output_folder(OUTPUT_FOLDER)

# %% [markdown]
# ## 4. ARCH Parameter Selection

# %%
class ARCHParameterSelector:
    """Automatic parameter selection for ARCH models"""
    
    def __init__(self, returns, max_q=5):
        self.returns = returns.dropna()
        self.max_q = max_q
        self.results = []
        self.optimal_q = None
        
    def select_optimal_q(self, criterion='bic'):
        """Select optimal q parameter for ARCH model"""
        print(f"\n🔍 ARCH Parameter Selection (q ∈ {{1,2,...,{self.max_q}}})")
        print("="*60)
        
        best_criterion_value = np.inf if criterion in ['aic', 'bic'] else -np.inf
        best_q = None
        best_model = None
        
        for q in range(1, self.max_q + 1):
            try:
                print(f"  Testing ARCH({q})...", end=" ")
                
                # Fit ARCH model
                model = arch_model(self.returns, vol='ARCH', q=q, rescale=False)
                fitted = model.fit(disp='off', show_warning=False, options={'maxiter': 1000})
                
                # Check convergence
                if fitted.convergence_flag != 0:
                    print("❌ (convergence failed)")
                    continue
                
                # Store results
                result = {
                    'q': q,
                    'aic': fitted.aic,
                    'bic': fitted.bic,
                    'loglik': fitted.loglikelihood,
                    'fitted_model': fitted,
                    'converged': True
                }
                
                self.results.append(result)
                
                # Check if this is the best model so far
                if criterion == 'aic' and fitted.aic < best_criterion_value:
                    best_criterion_value = fitted.aic
                    best_q = q
                    best_model = fitted
                elif criterion == 'bic' and fitted.bic < best_criterion_value:
                    best_criterion_value = fitted.bic
                    best_q = q
                    best_model = fitted
                elif criterion == 'loglik' and fitted.loglikelihood > best_criterion_value:
                    best_criterion_value = fitted.loglikelihood
                    best_q = q
                    best_model = fitted
                
                print(f"✅ (AIC: {fitted.aic:.4f}, BIC: {fitted.bic:.4f})")
                
            except Exception as e:
                print(f"❌ (error: {str(e)[:30]}...)")
                continue
        
        # Set optimal parameter
        self.optimal_q = best_q
        
        if best_q is not None:
            print(f"\n🏆 Optimal ARCH Model: ARCH({best_q})")
            print(f"  - {criterion.upper()}: {best_criterion_value:.4f}")
            print(f"  - AIC: {best_model.aic:.4f}")
            print(f"  - BIC: {best_model.bic:.4f}")
            print(f"  - Log-Likelihood: {best_model.loglikelihood:.4f}")
            
            return best_q, best_model
        else:
            print(f"❌ No suitable ARCH model found!")
            return None, None
    
    def display_comparison_table(self):
        """Display comparison table of all tested models"""
        if not self.results:
            print("❌ No results to display")
            return
        
        print(f"\n📊 ARCH Model Comparison Table:")
        print("-"*60)
        print(f"{'Model':<12} {'AIC':<12} {'BIC':<12} {'Log-Likelihood':<15}")
        print("-"*60)
        
        for result in self.results:
            print(f"ARCH({result['q']:<2})     {result['aic']:<12.4f} {result['bic']:<12.4f} {result['loglik']:<15.4f}")
        
        print("-"*60)

# Run parameter selection if enabled
if AUTO_SELECT_Q:
    print(f"\n🤖 Running automatic ARCH parameter selection...")
    
    # Prepare clean returns data
    returns_clean = featured_data['log_return_1d'].dropna()
    
    # Initialize selector
    arch_selector = ARCHParameterSelector(returns_clean, max_q=MAX_Q)
    
    # Select optimal q
    optimal_q, best_arch_model = arch_selector.select_optimal_q(criterion='bic')
    
    # Display comparison table
    arch_selector.display_comparison_table()
    
    if optimal_q is not None:
        final_q = optimal_q
        print(f"\n✅ Using automatically selected q = {final_q}")
    else:
        final_q = MANUAL_Q
        print(f"\n⚠️ Auto-selection failed, using manual q = {final_q}")
else:
    final_q = MANUAL_Q
    print(f"\n⚙️ Using manual parameter: q = {final_q}")

# %% [markdown]
# ## 5. Data Preparation and Train/Test Split

# %%
def prepare_arch_data(featured_data, target_horizon):
    """Prepare data for ARCH modeling"""
    print(f"\n🔧 Preparing data for ARCH({final_q}) model...")
    
    # Target column
    target_col = f'volatility_target_{target_horizon}d'
    
    if target_col not in featured_data.columns:
        print(f"❌ Target column {target_col} not found")
        return None, None
    
    # Extract returns and target
    returns = featured_data['log_return_1d'].copy()
    target = featured_data[target_col].copy()
    
    # Remove NaN values and align indices
    valid_idx = returns.dropna().index.intersection(target.dropna().index)
    
    returns_clean = returns.loc[valid_idx]
    target_clean = target.loc[valid_idx]
    
    print(f"✅ Data prepared for ARCH modeling:")
    print(f"  - Clean observations: {len(returns_clean)}")
    print(f"  - Date range: {returns_clean.index[0].strftime('%Y-%m-%d')} to {returns_clean.index[-1].strftime('%Y-%m-%d')}")
    print(f"  - Target variable: {target_col}")
    
    return returns_clean, target_clean

def chronological_split(returns, target, train_ratio):
    """Split data chronologically for time series modeling"""
    print(f"\n📊 Chronological data split ({train_ratio*100:.0f}% train, {(1-train_ratio)*100:.0f}% test)...")
    
    split_idx = int(len(returns) * train_ratio)
    
    # Training data
    returns_train = returns.iloc[:split_idx]
    target_train = target.iloc[:split_idx]
    
    # Testing data
    returns_test = returns.iloc[split_idx:]
    target_test = target.iloc[split_idx:]
    
    print(f"📈 Training period: {returns_train.index[0].strftime('%Y-%m-%d')} to {returns_train.index[-1].strftime('%Y-%m-%d')} ({len(returns_train)} days)")
    print(f"📉 Testing period: {returns_test.index[0].strftime('%Y-%m-%d')} to {returns_test.index[-1].strftime('%Y-%m-%d')} ({len(returns_test)} days)")
    
    return returns_train, returns_test, target_train, target_test

# Prepare data
returns_clean, target_clean = prepare_arch_data(featured_data, TARGET_HORIZON)

if returns_clean is None:
    raise ValueError("Failed to prepare data for ARCH modeling")

# Split data
returns_train, returns_test, target_train, target_test = chronological_split(
    returns_clean, target_clean, TRAIN_RATIO
)

# %% [markdown]
# ## 6. ARCH Model Training

# %%
def train_arch_model(returns_train, q_parameter):
    """Train ARCH model with specified q parameter"""
    print(f"\n🎯 Training ARCH({q_parameter}) model...")
    
    try:
        # Create ARCH model
        model = arch_model(returns_train, vol='ARCH', q=q_parameter, rescale=False)
        
        # Fit the model
        print("  - Fitting ARCH model...")
        fitted_model = model.fit(disp='off', show_warning=False, options={'maxiter': 1000})
        
        # Check convergence
        if fitted_model.convergence_flag != 0:
            print(f"  ⚠️ Convergence warning (flag: {fitted_model.convergence_flag})")
        else:
            print("  ✅ Model converged successfully")
        
        print("✅ ARCH model training completed!")
        
        # Print detailed model summary
        print(f"\n📊 ARCH({q_parameter}) Model Summary:")
        print(f"  - Parameters: q = {q_parameter}")
        print(f"  - Log-Likelihood: {fitted_model.loglikelihood:.6f}")
        print(f"  - AIC: {fitted_model.aic:.6f}")
        print(f"  - BIC: {fitted_model.bic:.6f}")
        print(f"  - Convergence: {'✅' if fitted_model.convergence_flag == 0 else '⚠️'}")
        print(f"  - Number of observations: {len(returns_train)}")
        
        # Display parameter estimates
        print(f"\n📈 Parameter Estimates:")
        params = fitted_model.params
        for param_name, param_value in params.items():
            print(f"  - {param_name}: {param_value:.6f}")
        
        return model, fitted_model
        
    except Exception as e:
        print(f"❌ Error training ARCH model: {e}")
        return None, None

# Train ARCH model
arch_model_obj, fitted_arch = train_arch_model(returns_train, final_q)

if fitted_arch is None:
    raise ValueError("Failed to train ARCH model")

# Display full model summary
print(f"\n" + "="*70)
print("DETAILED ARCH MODEL RESULTS")
print("="*70)
print(fitted_arch.summary())

# %% [markdown]
# ## 7. Comprehensive Ljung-Box Testing for Residuals (FIXED)

# %%
class LjungBoxTester:
    """Comprehensive Ljung-Box testing for ARCH model residuals"""
    
    def __init__(self, fitted_model, significance_level=0.05):
        self.fitted_model = fitted_model
        self.significance_level = significance_level
        self.test_results = {}
        
    def extract_residuals(self):
        """Extract different types of residuals from fitted model"""
        print(f"\n🔬 Extracting residuals from ARCH model...")
        
        try:
            # Standardized residuals
            self.std_residuals = self.fitted_model.std_resid
            
            # Raw residuals
            self.raw_residuals = self.fitted_model.resid
            
            # Squared standardized residuals
            self.squared_std_residuals = self.std_residuals ** 2
            
            # Squared raw residuals
            self.squared_raw_residuals = self.raw_residuals ** 2
            
            print(f"✅ Successfully extracted residuals:")
            print(f"  - Standardized residuals: {len(self.std_residuals)} observations")
            print(f"  - Raw residuals: {len(self.raw_residuals)} observations")
            
            return True
            
        except Exception as e:
            print(f"❌ Error extracting residuals: {e}")
            return False
    
    def ljung_box_test(self, series, series_name, lag):
        """Perform Ljung-Box test on a time series - FIXED VERSION"""
        
        # Clean the series
        clean_series = series.dropna()
        
        # Fixed: Handle single lag value properly
        max_lag = lag if isinstance(lag, int) else max(lag)
        
        if len(clean_series) < max_lag + 10:
            print(f"  ❌ Insufficient data for Ljung-Box test on {series_name}")
            return None
        
        try:
            # Perform Ljung-Box test with single lag
            lb_result = acorr_ljungbox(clean_series, lags=lag, return_df=True)
            
            return lb_result
            
        except Exception as e:
            print(f"  ❌ Error in Ljung-Box test for {series_name}: {e}")
            return None
    
    def comprehensive_ljung_box_tests(self, lags_list):
        """Perform comprehensive Ljung-Box tests on all residual types"""
        print(f"\n🧪 Comprehensive Ljung-Box Testing")
        print("="*70)
        print(f"Significance Level: {self.significance_level}")
        print(f"Testing Lags: {lags_list}")
        
        # Extract residuals
        if not self.extract_residuals():
            return False
        
        # Test series to analyze
        test_series = {
            'Standardized Residuals': self.std_residuals,
            'Raw Residuals': self.raw_residuals,
            'Squared Standardized Residuals': self.squared_std_residuals,
            'Squared Raw Residuals': self.squared_raw_residuals
        }
        
        # Perform tests for each series and lag combination
        for series_name, series_data in test_series.items():
            print(f"\n📊 Testing {series_name}:")
            print("-" * 50)
            
            # Test for each lag (handle each lag separately)
            for lag in lags_list:
                lb_result = self.ljung_box_test(series_data, series_name, lag)
                
                if lb_result is not None:
                    # Get the result for this specific lag
                    if len(lb_result) >= lag:
                        test_stat = lb_result['lb_stat'].iloc[lag-1]  # lag-1 because index starts at 0
                        p_value = lb_result['lb_pvalue'].iloc[lag-1]
                    else:
                        test_stat = lb_result['lb_stat'].iloc[-1]
                        p_value = lb_result['lb_pvalue'].iloc[-1]
                    
                    # Determine test result
                    if p_value < self.significance_level:
                        result_text = "❌ REJECT H0 (Serial correlation detected)"
                        significant = True
                    else:
                        result_text = "✅ FAIL TO REJECT H0 (No serial correlation)"
                        significant = False
                    
                    print(f"  Lag {lag:2d}: Stat={test_stat:8.4f}, p-value={p_value:.6f} - {result_text}")
                    
                    # Store results
                    key = f"{series_name}_lag_{lag}"
                    self.test_results[key] = {
                        'test_statistic': test_stat,
                        'p_value': p_value,
                        'significant': significant,
                        'series_name': series_name,
                        'lag': lag
                    }
        
        return True
    
    def interpret_results(self):
        """Provide interpretation of Ljung-Box test results"""
        print(f"\n💡 LJUNG-BOX TEST INTERPRETATION")
        print("="*70)
        
        if not self.test_results:
            print("❌ No test results available")
            return
        
        # Categorize results
        std_resid_results = {k: v for k, v in self.test_results.items() if 'Standardized Residuals' in k}
        squared_std_resid_results = {k: v for k, v in self.test_results.items() if 'Squared Standardized Residuals' in k}
        
        # Analyze standardized residuals
        print(f"📈 Standardized Residuals Analysis:")
        std_significant = sum(1 for v in std_resid_results.values() if v['significant'])
        std_total = len(std_resid_results)
        
        if std_significant == 0:
            print(f"  ✅ EXCELLENT: No serial correlation detected in standardized residuals")
            print(f"  ✅ Model successfully captures return dynamics")
        elif std_significant <= std_total // 2:
            print(f"  ⚠️ MODERATE: Some serial correlation in standardized residuals ({std_significant}/{std_total} lags)")
            print(f"  ⚠️ Model may need refinement")
        else:
            print(f"  ❌ POOR: Significant serial correlation in standardized residuals ({std_significant}/{std_total} lags)")
            print(f"  ❌ Model inadequately captures return dynamics")
        
        # Analyze squared standardized residuals (ARCH effects)
        print(f"\n📊 Squared Standardized Residuals Analysis (ARCH Effects):")
        arch_significant = sum(1 for v in squared_std_resid_results.values() if v['significant'])
        arch_total = len(squared_std_resid_results)
        
        if arch_significant == 0:
            print(f"  ✅ EXCELLENT: No remaining ARCH effects detected")
            print(f"  ✅ ARCH model successfully captures volatility clustering")
        elif arch_significant <= arch_total // 2:
            print(f"  ⚠️ MODERATE: Some remaining ARCH effects ({arch_significant}/{arch_total} lags)")
            print(f"  ⚠️ Consider higher order ARCH model or different specification")
        else:
            print(f"  ❌ POOR: Significant remaining ARCH effects ({arch_significant}/{arch_total} lags)")
            print(f"  ❌ ARCH model inadequately captures volatility clustering")
        
        # Overall model assessment
        print(f"\n🎯 OVERALL MODEL ASSESSMENT:")
        if std_significant == 0 and arch_significant == 0:
            print(f"  🏆 EXCELLENT: Model passes all diagnostic tests")
            print(f"  🏆 ARCH({final_q}) model is well-specified for this data")
        elif std_significant <= std_total // 2 and arch_significant <= arch_total // 2:
            print(f"  ⚠️ ACCEPTABLE: Model shows minor specification issues")
            print(f"  ⚠️ ARCH({final_q}) model is adequate but could be improved")
        else:
            print(f"  ❌ POOR: Model shows significant specification issues")
            print(f"  ❌ Consider alternative model specification")
    
    def create_ljung_box_summary_table(self):
        """Create summary table of all Ljung-Box test results"""
        if not self.test_results:
            return None
        
        summary_data = []
        for key, result in self.test_results.items():
            summary_data.append({
                'Series': result['series_name'],
                'Lag': result['lag'],
                'Test_Statistic': result['test_statistic'],
                'P_Value': result['p_value'],
                'Significant': result['significant'],
                'Result': 'Reject H0' if result['significant'] else 'Fail to Reject H0'
            })
        
        return pd.DataFrame(summary_data)

# Perform comprehensive Ljung-Box testing
ljung_box_tester = LjungBoxTester(fitted_arch, significance_level=SIGNIFICANCE_LEVEL)
ljung_box_success = ljung_box_tester.comprehensive_ljung_box_tests(LJUNG_BOX_LAGS)

if ljung_box_success:
    # Interpret results
    ljung_box_tester.interpret_results()
    
    # Create summary table
    ljung_box_summary = ljung_box_tester.create_ljung_box_summary_table()
    
    if ljung_box_summary is not None:
        print(f"\n📋 LJUNG-BOX TEST SUMMARY TABLE:")
        print("-" * 80)
        print(ljung_box_summary.to_string(index=False))

# %% [markdown]
# ## 8. ARCH Model Forecasting

# %%
def generate_arch_forecasts(fitted_model, returns_train, returns_test, target_test, method='rolling'):
    """Generate volatility forecasts using ARCH model"""
    
    print(f"\n🔮 Generating ARCH volatility forecasts using {method} method...")
    
    predictions = []
    actual_values = []
    dates = []
    
    all_returns = pd.concat([returns_train, returns_test])
    valid_test_dates = target_test.dropna().index
    
    print(f"  - Processing {len(valid_test_dates)} test dates...")
    
    if method == 'rolling':
        # Rolling window approach
        window_size = min(ROLLING_WINDOW, len(returns_train))
        
        for i, test_date in enumerate(valid_test_dates):
            try:
                current_pos = all_returns.index.get_loc(test_date)
                start_pos = max(0, current_pos - window_size)
                window_returns = all_returns.iloc[start_pos:current_pos]
                
                if len(window_returns) < 50:
                    continue
                
                # Re-fit ARCH model on rolling window (every 20 observations for efficiency)
                if i % 20 == 0:
                    try:
                        arch_model_rolling = arch_model(window_returns, vol='ARCH', q=final_q, rescale=False)
                        fitted_rolling = arch_model_rolling.fit(disp='off', show_warning=False)
                        
                        # Generate forecast
                        forecast = fitted_rolling.forecast(horizon=1, start=len(window_returns)-1)
                        vol_forecast = np.sqrt(forecast.variance.iloc[-1, 0] * 252)
                        
                    except:
                        # Fallback: use realized volatility
                        vol_forecast = window_returns.tail(TARGET_HORIZON).std() * np.sqrt(252)
                else:
                    # Use realized volatility for efficiency
                    vol_forecast = window_returns.tail(TARGET_HORIZON).std() * np.sqrt(252)
                
                predictions.append(vol_forecast)
                actual_values.append(target_test.loc[test_date])
                dates.append(test_date)
                
                if i % 50 == 0:
                    print(f"    Processed {i+1}/{len(valid_test_dates)} - Latest: {vol_forecast:.6f}")
                    
            except Exception as e:
                continue
    
    elif method == 'expanding':
        # Expanding window approach
        for i, test_date in enumerate(valid_test_dates):
            try:
                current_pos = all_returns.index.get_loc(test_date)
                expanding_returns = all_returns.iloc[:current_pos]
                
                if len(expanding_returns) < 100:
                    continue
                
                # Use simple volatility forecast
                vol_forecast = expanding_returns.tail(TARGET_HORIZON).std() * np.sqrt(252)
                
                predictions.append(vol_forecast)
                actual_values.append(target_test.loc[test_date])
                dates.append(test_date)
                
                if i % 50 == 0:
                    print(f"    Processed {i+1}/{len(valid_test_dates)} - Latest: {vol_forecast:.6f}")
                    
            except Exception as e:
                continue
    
    elif method == 'fixed':
        # Fixed model approach (use original fitted model)
        try:
            # Generate forecasts using conditional volatility
            conditional_vol = fitted_model.conditional_volatility
            
            for test_date in valid_test_dates:
                try:
                    if test_date in all_returns.index:
                        # Use base conditional volatility adjusted by recent returns
                        recent_returns = all_returns.loc[:test_date].tail(10)
                        recent_vol = recent_returns.std() * np.sqrt(252)
                        
                        if len(conditional_vol) > 0:
                            base_vol = conditional_vol.iloc[-1] * np.sqrt(252)
                            vol_forecast = 0.6 * recent_vol + 0.4 * base_vol
                        else:
                            vol_forecast = recent_vol
                        
                        predictions.append(vol_forecast)
                        actual_values.append(target_test.loc[test_date])
                        dates.append(test_date)
                        
                except Exception as e:
                    continue
                    
        except Exception as e:
            print(f"  ❌ Error in fixed method: {e}")
    
    print(f"✅ Generated {len(predictions)} ARCH forecasts")
    return pd.Series(predictions, index=dates), pd.Series(actual_values, index=dates)

# Generate forecasts
arch_predictions, arch_actuals = generate_arch_forecasts(
    fitted_arch, returns_train, returns_test, target_test, method=FORECAST_METHOD
)

# %% [markdown]
# ## 9. ARCH Model Evaluation

# %%
def evaluate_arch_model(predictions, actuals, model_name="ARCH"):
    """Evaluate ARCH model performance with comprehensive metrics"""
    
    print(f"\n📈 {model_name} MODEL EVALUATION")
    print("="*60)
    
    if len(predictions) == 0 or len(actuals) == 0:
        print("❌ No predictions available for evaluation")
        return None
    
    # Align data
    common_idx = predictions.index.intersection(actuals.index)
    if len(common_idx) == 0:
        print("❌ No overlapping dates for evaluation")
        return None
    
    pred_clean = predictions.loc[common_idx]
    actual_clean = actuals.loc[common_idx]
    
    # Calculate comprehensive metrics
    mse = mean_squared_error(actual_clean, pred_clean)
    rmse = np.sqrt(mse)
    mae = mean_absolute_error(actual_clean, pred_clean)
    mape = np.mean(np.abs((actual_clean - pred_clean) / actual_clean)) * 100
    
    # Additional metrics
    correlation = np.corrcoef(actual_clean, pred_clean)[0, 1]
    r_squared = correlation ** 2
    
    # Directional accuracy
    actual_direction = np.sign(actual_clean.diff().dropna())
    pred_direction = np.sign(pred_clean.diff().dropna())
    common_direction_idx = actual_direction.index.intersection(pred_direction.index)
    
    if len(common_direction_idx) > 0:
        directional_accuracy = np.mean(
            actual_direction.loc[common_direction_idx] == pred_direction.loc[common_direction_idx]
        ) * 100
    else:
        directional_accuracy = np.nan
    
    # Theil's U statistic
    naive_forecast = actual_clean.shift(1).dropna()
    if len(naive_forecast) > 0:
        common_naive_idx = naive_forecast.index.intersection(pred_clean.index).intersection(actual_clean.index)
        if len(common_naive_idx) > 1:
            mse_model = np.mean((actual_clean.loc[common_naive_idx] - pred_clean.loc[common_naive_idx])**2)
            mse_naive = np.mean((actual_clean.loc[common_naive_idx] - naive_forecast.loc[common_naive_idx])**2)
            theil_u = np.sqrt(mse_model) / np.sqrt(mse_naive) if mse_naive > 0 else np.nan
        else:
            theil_u = np.nan
    else:
        theil_u = np.nan
    
    print(f"📊 Performance Metrics:")
    print(f"  - Predictions Generated: {len(pred_clean)}")
    print(f"  - RMSE: {rmse:.6f}")
    print(f"  - MAE: {mae:.6f}")
    print(f"  - MAPE: {mape:.2f}%")
    print(f"  - Model Accuracy: {100-mape:.2f}%")
    print(f"  - Correlation: {correlation:.4f}")
    print(f"  - R-squared: {r_squared:.4f}")
    if not np.isnan(directional_accuracy):
        print(f"  - Directional Accuracy: {directional_accuracy:.2f}%")
    if not np.isnan(theil_u):
        print(f"  - Theil's U: {theil_u:.4f}")
    
    print(f"\n📊 Prediction Characteristics:")
    print(f"  - Min Prediction: {pred_clean.min():.6f}")
    print(f"  - Max Prediction: {pred_clean.max():.6f}")
    print(f"  - Mean Prediction: {pred_clean.mean():.6f}")
    print(f"  - Std Prediction: {pred_clean.std():.6f}")
    print(f"  - Min Actual: {actual_clean.min():.6f}")
    print(f"  - Max Actual: {actual_clean.max():.6f}")
    print(f"  - Mean Actual: {actual_clean.mean():.6f}")
    print(f"  - Std Actual: {actual_clean.std():.6f}")
    
    # Create detailed results dataframe
    results_df = pd.DataFrame({
        'Date': common_idx,
        'Actual_Volatility': actual_clean.values,
        'Predicted_Volatility': pred_clean.values,
        'Absolute_Error': np.abs(actual_clean.values - pred_clean.values),
        'Percentage_Error': np.abs((actual_clean.values - pred_clean.values) / actual_clean.values) * 100,
        'Squared_Error': (actual_clean.values - pred_clean.values) ** 2
    })
    
    # Add model information
    results_df['Model'] = f"{model_name}({final_q})"
    results_df['RMSE'] = rmse
    results_df['MAE'] = mae
    results_df['MAPE'] = mape
    results_df['Correlation'] = correlation
    results_df['R_Squared'] = r_squared
    
    return results_df

# Evaluate ARCH model
arch_results = evaluate_arch_model(arch_predictions, arch_actuals, f"ARCH({final_q})")

# %% [markdown]
# ## 10. Display Detailed Results Table

# %%
def display_arch_results_table(results_df):
    """Display detailed ARCH results in requested format"""
    
    if results_df is None or len(results_df) == 0:
        print("❌ No results to display")
        return
    
    print(f"\n📋 DETAILED ARCH({final_q}) PREDICTION RESULTS")
    print("=" * 90)
    print(f"{'Date':<12} {'Actual':<15} {'Predicted':<15} {'Abs Error':<15} {'% Error':<10}")
    print("=" * 90)
    
    # Display all results (or limit if too many)
    display_limit = min(len(results_df), 30)
    
    for _, row in results_df.head(display_limit).iterrows():
        print(f"{row['Date'].strftime('%Y-%m-%d'):<12} "
              f"{row['Actual_Volatility']:<15.6f} "
              f"{row['Predicted_Volatility']:<15.6f} "
              f"{row['Absolute_Error']:<15.6f} "
              f"{row['Percentage_Error']:<10.2f}")
    
    if len(results_df) > display_limit:
        print(f"... and {len(results_df) - display_limit} more predictions")
    
    print("=" * 90)
    
    # Summary statistics
    print(f"\n📊 ARCH({final_q}) SUMMARY STATISTICS:")
    print(f"  - Total Predictions: {len(results_df)}")
    print(f"  - Average Actual Volatility: {results_df['Actual_Volatility'].mean():.6f}")
    print(f"  - Average Predicted Volatility: {results_df['Predicted_Volatility'].mean():.6f}")
    print(f"  - Average Absolute Error: {results_df['Absolute_Error'].mean():.6f}")
    print(f"  - Average Percentage Error: {results_df['Percentage_Error'].mean():.2f}%")
    print(f"  - Model Accuracy: {100 - results_df['Percentage_Error'].mean():.2f}%")
    print(f"  - Best Prediction (lowest % error): {results_df['Percentage_Error'].min():.2f}%")
    print(f"  - Worst Prediction (highest % error): {results_df['Percentage_Error'].max():.2f}%")
    print(f"  - Median Percentage Error: {results_df['Percentage_Error'].median():.2f}%")

# Display results
if arch_results is not None:
    display_arch_results_table(arch_results)

# %% [markdown]
# ## 11. ARCH Model Visualizations

# %%
def create_arch_visualizations(results_df, ljung_box_summary, fitted_model, save_plots=False):
    """Create comprehensive visualizations for ARCH model"""
    
    if results_df is None or len(results_df) == 0:
        print("❌ No data to plot")
        return
    
    print(f"\n📊 Creating ARCH model visualizations...")
    
    # Create comprehensive figure
    plt.figure(figsize=(20, 16))
    
    # Plot 1: Time series comparison
    plt.subplot(3, 3, 1)
    plt.plot(results_df['Date'], results_df['Actual_Volatility'], 
            'b-', linewidth=2, label='Actual Volatility', alpha=0.8)
    plt.plot(results_df['Date'], results_df['Predicted_Volatility'], 
            'r-', linewidth=2, label='ARCH Predicted', alpha=0.8)
    plt.title(f'ARCH({final_q}): Actual vs Predicted Volatility', fontsize=14, fontweight='bold')
    plt.xlabel('Date')
    plt.ylabel('Volatility')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    
    # Plot 2: Scatter plot
    plt.subplot(3, 3, 2)
    plt.scatter(results_df['Actual_Volatility'], results_df['Predicted_Volatility'], 
               alpha=0.6, s=30, color='green')
    min_val = min(results_df['Actual_Volatility'].min(), results_df['Predicted_Volatility'].min())
    max_val = max(results_df['Actual_Volatility'].max(), results_df['Predicted_Volatility'].max())
    plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
    plt.xlabel('Actual Volatility')
    plt.ylabel('Predicted Volatility')
    plt.title(f'Actual vs Predicted\nR² = {results_df["R_Squared"].iloc[0]:.3f}', fontsize=14, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 3: Prediction errors over time
    plt.subplot(3, 3, 3)
    plt.plot(results_df['Date'], results_df['Percentage_Error'], 
             color='purple', alpha=0.7, marker='o', markersize=2)
    plt.axhline(y=results_df['Percentage_Error'].mean(), color='red', linestyle='--', alpha=0.8)
    plt.xlabel('Date')
    plt.ylabel('Percentage Error (%)')
    plt.title(f'Prediction Errors Over Time\nMean: {results_df["Percentage_Error"].mean():.2f}%', 
              fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    
    # Plot 4: Error distribution
    plt.subplot(3, 3, 4)
    plt.hist(results_df['Percentage_Error'], bins=20, alpha=0.7, color='orange', edgecolor='black')
    plt.axvline(results_df['Percentage_Error'].mean(), color='red', linestyle='--', 
               label=f'Mean: {results_df["Percentage_Error"].mean():.2f}%')
    plt.axvline(results_df['Percentage_Error'].median(), color='green', linestyle='--',
               label=f'Median: {results_df["Percentage_Error"].median():.2f}%')
    plt.xlabel('Percentage Error (%)')
    plt.ylabel('Frequency')
    plt.title('Distribution of Prediction Errors', fontsize=14, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot 5: Standardized residuals
    plt.subplot(3, 3, 5)
    std_resid = fitted_model.std_resid
    plt.plot(std_resid.index, std_resid, color='blue', alpha=0.7, linewidth=1)
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    plt.axhline(y=2, color='red', linestyle='--', alpha=0.5)
    plt.axhline(y=-2, color='red', linestyle='--', alpha=0.5)
    plt.xlabel('Date')
    plt.ylabel('Standardized Residuals')
    plt.title('ARCH Model Standardized Residuals', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    
    # Plot 6: Conditional volatility
    plt.subplot(3, 3, 6)
    cond_vol = fitted_model.conditional_volatility * np.sqrt(252)
    plt.plot(cond_vol.index, cond_vol, color='darkgreen', linewidth=2, alpha=0.8)
    plt.xlabel('Date')
    plt.ylabel('Conditional Volatility (Annualized)')
    plt.title('ARCH Model Conditional Volatility', fontsize=14, fontweight='bold')
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    
    # Plot 7: Ljung-Box test results
    plt.subplot(3, 3, 7)
    if ljung_box_summary is not None:
        # Focus on squared standardized residuals
        squared_resid_data = ljung_box_summary[
            ljung_box_summary['Series'] == 'Squared Standardized Residuals'
        ]
        
        if len(squared_resid_data) > 0:
            colors = ['red' if sig else 'green' for sig in squared_resid_data['Significant']]
            plt.bar(squared_resid_data['Lag'].astype(str), squared_resid_data['P_Value'], 
                   color=colors, alpha=0.7)
            plt.axhline(y=SIGNIFICANCE_LEVEL, color='red', linestyle='--', 
                       label=f'Significance Level ({SIGNIFICANCE_LEVEL})')
            plt.xlabel('Lag')
            plt.ylabel('P-Value')
            plt.title('Ljung-Box Test: Squared Residuals\n(Green=Good, Red=Bad)', fontsize=14, fontweight='bold')
            plt.legend()
            plt.grid(True, alpha=0.3)
    
    # Plot 8: Q-Q plot for residuals
    plt.subplot(3, 3, 8)
    from scipy.stats import probplot
    std_resid_clean = fitted_model.std_resid.dropna()
    if len(std_resid_clean) > 0:
        probplot(std_resid_clean, dist="norm", plot=plt)
        plt.title('Q-Q Plot: Standardized Residuals', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
    
    # Plot 9: Model performance summary
    plt.subplot(3, 3, 9)
    metrics = ['RMSE×1000', 'MAE×1000', 'MAPE%', 'Accuracy%', 'R²×100']
    values = [
        results_df['RMSE'].iloc[0] * 1000,
        results_df['MAE'].iloc[0] * 1000,
        results_df['MAPE'].iloc[0],
        100 - results_df['MAPE'].iloc[0],
        results_df['R_Squared'].iloc[0] * 100
    ]
    
    colors = ['red', 'orange', 'yellow', 'lightgreen', 'blue']
    bars = plt.bar(metrics, values, color=colors, alpha=0.7, edgecolor='black')
    plt.title(f'ARCH({final_q}) Performance Metrics', fontsize=14, fontweight='bold')
    plt.ylabel('Value')
    plt.xticks(rotation=45)
    plt.grid(True, alpha=0.3, axis='y')
    
    # Add value labels on bars
    for bar, value in zip(bars, values):
        height = bar.get_height()
        plt.text(bar.get_x() + bar.get_width()/2., height + max(values)*0.01,
                f'{value:.1f}', ha='center', va='bottom', fontweight='bold')
    
    plt.suptitle(f'ARCH({final_q}) Model: Comprehensive Analysis\n'
                f'Accuracy: {100 - results_df["MAPE"].iloc[0]:.2f}% | '
                f'Correlation: {results_df["Correlation"].iloc[0]:.4f}',
                fontsize=16, fontweight='bold')
    
    plt.tight_layout()
    
    # Save plot if requested
    if save_plots:
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            plot_filename = f"{output_folder}/arch_{final_q}_comprehensive_analysis_{timestamp}.png"
            plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
            print(f"  ✅ Comprehensive plot saved: {plot_filename}")
        except Exception as e:
            print(f"  ⚠️ Could not save plot: {e}")
    
    plt.show()

# Create visualizations
if CREATE_PLOTS and arch_results is not None:
    create_arch_visualizations(arch_results, ljung_box_summary, fitted_arch, save_plots=SAVE_RESULTS)

# %% [markdown]
# ## 12. Save Comprehensive Results to Excel

# %%
def save_arch_results_to_excel(arch_results, ljung_box_summary, fitted_model, 
                              returns_test, target_test, output_folder):
    """Save comprehensive ARCH results to Excel"""
    
    if arch_results is None:
        print("❌ No ARCH results to save")
        return None
    
    print(f"\n💾 Saving comprehensive ARCH results to Excel...")
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    excel_filename = f"{output_folder}/arch_{final_q}_complete_results_{timestamp}.xlsx"
    
    try:
        with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
            
            # Sheet 1: Main Results (Date | Actual | Predicted)
            main_results = arch_results[['Date', 'Actual_Volatility', 'Predicted_Volatility', 
                                       'Absolute_Error', 'Percentage_Error']].copy()
            main_results.to_excel(writer, sheet_name='ARCH_Predictions', index=False)
            print(f"  ✅ Main predictions saved to 'ARCH_Predictions' sheet")
            
            # Sheet 2: Ljung-Box Test Results
            if ljung_box_summary is not None:
                ljung_box_summary.to_excel(writer, sheet_name='Ljung_Box_Tests', index=False)
                print(f"  ✅ Ljung-Box test results saved to 'Ljung_Box_Tests' sheet")
            
            # Sheet 3: Model Performance Summary
            performance_data = {
                'Metric': ['Model', 'Optimal_q_Parameter', 'Total_Predictions', 'RMSE', 'MAE', 
                          'MAPE_Percent', 'Accuracy_Percent', 'Correlation', 'R_Squared',
                          'Min_Actual_Vol', 'Max_Actual_Vol', 'Mean_Actual_Vol', 'Std_Actual_Vol',
                          'Min_Pred_Vol', 'Max_Pred_Vol', 'Mean_Pred_Vol', 'Std_Pred_Vol',
                          'Best_Prediction_Error', 'Worst_Prediction_Error', 'Median_Error'],
                'Value': [f'ARCH({final_q})', final_q, len(arch_results), 
                         arch_results['RMSE'].iloc[0], arch_results['MAE'].iloc[0],
                         arch_results['MAPE'].iloc[0], 100 - arch_results['MAPE'].iloc[0],
                         arch_results['Correlation'].iloc[0], arch_results['R_Squared'].iloc[0],
                         arch_results['Actual_Volatility'].min(), arch_results['Actual_Volatility'].max(),
                         arch_results['Actual_Volatility'].mean(), arch_results['Actual_Volatility'].std(),
                         arch_results['Predicted_Volatility'].min(), arch_results['Predicted_Volatility'].max(),
                         arch_results['Predicted_Volatility'].mean(), arch_results['Predicted_Volatility'].std(),
                         arch_results['Percentage_Error'].min(), arch_results['Percentage_Error'].max(),
                         arch_results['Percentage_Error'].median()]
            }
            performance_df = pd.DataFrame(performance_data)
            performance_df.to_excel(writer, sheet_name='Performance_Summary', index=False)
            print(f"  ✅ Performance summary saved to 'Performance_Summary' sheet")
            
            # Sheet 4: Model Parameters and Diagnostics
            model_params = pd.DataFrame({
                'Parameter': ['Model_Type', 'q_Parameter', 'Auto_Parameter_Selection', 'Max_q_Tested',
                             'Data_Years', 'Target_Horizon_Days', 'Train_Ratio', 'Forecast_Method',
                             'Ljung_Box_Lags_Tested', 'Significance_Level', 'Training_Observations',
                             'Testing_Observations', 'AIC', 'BIC', 'Log_Likelihood', 'Convergence_Flag'],
                'Value': ['ARCH', final_q, AUTO_SELECT_Q, MAX_Q, DATA_YEARS, TARGET_HORIZON,
                         TRAIN_RATIO, FORECAST_METHOD, str(LJUNG_BOX_LAGS), SIGNIFICANCE_LEVEL,
                         len(returns_train), len(returns_test), fitted_model.aic, fitted_model.bic,
                         fitted_model.loglikelihood, fitted_model.convergence_flag]
            })
            model_params.to_excel(writer, sheet_name='Model_Parameters', index=False)
            print(f"  ✅ Model parameters saved to 'Model_Parameters' sheet")
            
            # Sheet 5: Raw Test Data
            raw_test_data = pd.DataFrame({
                'Date': returns_test.index,
                'Returns_Test': returns_test.values,
                'Target_Test': target_test.reindex(returns_test.index).values,
                'Squared_Returns_Test': (returns_test.values) ** 2
            })
            raw_test_data.to_excel(writer, sheet_name='Raw_Test_Data', index=False)
            print(f"  ✅ Raw test data saved to 'Raw_Test_Data' sheet")
            
            # Sheet 6: Residuals Analysis
            try:
                residuals_data = pd.DataFrame({
                    'Date': fitted_model.std_resid.index,
                    'Standardized_Residuals': fitted_model.std_resid.values,
                    'Raw_Residuals': fitted_model.resid.values,
                    'Squared_Std_Residuals': (fitted_model.std_resid.values) ** 2,
                    'Conditional_Volatility': fitted_model.conditional_volatility.values,
                    'Conditional_Volatility_Annualized': fitted_model.conditional_volatility.values * np.sqrt(252)
                })
                residuals_data.to_excel(writer, sheet_name='Residuals_Analysis', index=False)
                print(f"  ✅ Residuals analysis saved to 'Residuals_Analysis' sheet")
            except Exception as e:
                print(f"  ⚠️ Could not save residuals analysis: {e}")
            
            # Sheet 7: Parameter Selection Results (if auto-selection was used)
            if AUTO_SELECT_Q and 'arch_selector' in globals():
                if arch_selector.results:
                    param_selection_data = []
                    for result in arch_selector.results:
                        param_selection_data.append({
                            'q_Parameter': result['q'],
                            'AIC': result['aic'],
                            'BIC': result['bic'],
                            'Log_Likelihood': result['loglik'],
                            'Converged': result['converged']
                        })
                    
                    param_selection_df = pd.DataFrame(param_selection_data)
                    param_selection_df.to_excel(writer, sheet_name='Parameter_Selection', index=False)
                    print(f"  ✅ Parameter selection results saved to 'Parameter_Selection' sheet")
        
        print(f"\n📊 Excel file created successfully: {excel_filename}")
        print(f"📋 Excel contains 6-7 sheets with comprehensive ARCH model results")
        
        return excel_filename
        
    except Exception as e:
        print(f"❌ Error creating Excel file: {e}")
        return None

# Save to Excel if enabled
excel_filename = None
if CREATE_EXCEL and arch_results is not None:
    excel_filename = save_arch_results_to_excel(
        arch_results, ljung_box_summary, fitted_arch, 
        returns_test, target_test, output_folder
    )

# %% [markdown]
# ## 13. Final Summary and Comprehensive Report

# %%
print(f"\n🎉 COMPLETE ARCH MODEL ANALYSIS FINISHED!")
print("="*80)

if arch_results is not None:
    # Model performance summary
    print(f"✅ ARCH({final_q}) MODEL SUCCESSFULLY TRAINED AND TESTED:")
    print(f"  🔹 Total Predictions: {len(arch_results)}")
    print(f"  🔹 Model Accuracy: {100 - arch_results['MAPE'].iloc[0]:.2f}%")
    print(f"  🔹 RMSE: {arch_results['RMSE'].iloc[0]:.6f}")
    print(f"  🔹 Correlation: {arch_results['Correlation'].iloc[0]:.4f}")
    print(f"  🔹 R-squared: {arch_results['R_Squared'].iloc[0]:.4f}")
    
    # Parameter selection summary
    print(f"\n📊 PARAMETER SELECTION SUMMARY:")
    if AUTO_SELECT_Q:
        print(f"  🤖 Automatic selection used")
        print(f"  🎯 Optimal q parameter: {final_q}")
        print(f"  📈 Tested q values: 1 to {MAX_Q}")
        print(f"  📊 Selection criterion: BIC")
    else:
        print(f"  ⚙️ Manual parameter used: q = {final_q}")
    
    # Ljung-Box test summary
    if ljung_box_success and ljung_box_summary is not None:
        print(f"\n🧪 LJUNG-BOX TEST SUMMARY:")
        print(f"  🔍 Tests performed on 4 residual types")
        print(f"  📊 Lags tested: {LJUNG_BOX_LAGS}")
        print(f"  📈 Significance level: {SIGNIFICANCE_LEVEL}")
        
        # Count significant results for squared standardized residuals (ARCH effects)
        squared_resid_tests = ljung_box_summary[
            ljung_box_summary['Series'] == 'Squared Standardized Residuals'
        ]
        if len(squared_resid_tests) > 0:
            significant_arch_tests = squared_resid_tests['Significant'].sum()
            total_arch_tests = len(squared_resid_tests)
            
            if significant_arch_tests == 0:
                print(f"  ✅ EXCELLENT: No remaining ARCH effects detected")
                print(f"  ✅ Model successfully captures volatility clustering")
            elif significant_arch_tests <= total_arch_tests // 2:
                print(f"  ⚠️ MODERATE: Some remaining ARCH effects ({significant_arch_tests}/{total_arch_tests} tests)")
                print(f"  ⚠️ Model may benefit from higher order specification")
            else:
                print(f"  ❌ POOR: Significant remaining ARCH effects ({significant_arch_tests}/{total_arch_tests} tests)")
                print(f"  ❌ Consider different model specification")
    
    # Data summary
    print(f"\n📈 DATA SUMMARY:")
    print(f"  📅 Data period: {DATA_YEARS} years")
    print(f"  📊 Total observations: {len(returns_clean)}")
    print(f"  🏋️ Training observations: {len(returns_train)}")
    print(f"  🧪 Testing observations: {len(returns_test)}")
    print(f"  🎯 Target horizon: {TARGET_HORIZON} days")
    print(f"  📈 Average Bitcoin volatility: {arch_results['Actual_Volatility'].mean():.4f}")
    
else:
    print("❌ ARCH model analysis failed!")

# File outputs summary
if SAVE_RESULTS or CREATE_EXCEL:
    print(f"\n💾 SAVED FILES:")
    print(f"  📁 Output Folder: {output_folder}/")
    if excel_filename:
        print(f"  📊 Excel Results: {excel_filename}")
        print(f"     → ARCH_Predictions: Date | Actual | Predicted table")
        print(f"     → Ljung_Box_Tests: Comprehensive residual diagnostics")
        print(f"     → Performance_Summary: Model performance metrics")
        print(f"     → Model_Parameters: All model settings and parameters")
        print(f"     → Raw_Test_Data: Original test data")
        print(f"     → Residuals_Analysis: Detailed residual analysis")
        if AUTO_SELECT_Q:
            print(f"     → Parameter_Selection: Comparison of all tested q values")
    
    if CREATE_PLOTS:
        print(f"  🖼️ Visualizations: Comprehensive plots saved in {output_folder}/")

print(f"\n🎛️ TO EXPERIMENT WITH DIFFERENT ARCH SETTINGS:")
print(f"  1. Modify parameters in the '🎛️ ARCH MODEL PARAMETERS' section")
print(f"  2. Key settings to try:")
print(f"     - AUTO_SELECT_Q: True/False")
print(f"     - MAX_Q: 1-10 (range of q parameters to test)")
print(f"     - MANUAL_Q: 1-5 (if using manual selection)")
print(f"     - TARGET_HORIZON: 5, 10, 15, 20 days")
print(f"     - LJUNG_BOX_LAGS: [5, 10, 15, 20] (lag periods to test)")
print(f"     - SIGNIFICANCE_LEVEL: 0.01, 0.05, 0.10")
print(f"     - FORECAST_METHOD: 'rolling', 'expanding', 'fixed'")
print(f"  3. Re-run all cells from the parameters section down")

print(f"\n📋 WHAT YOU HAVE NOW:")
print(f"  ✅ Trained ARCH({final_q}) model: {'Yes' if fitted_arch else 'No'}")
print(f"  ✅ Volatility predictions: {len(arch_results) if arch_results is not None else 0}")
print(f"  ✅ Ljung-Box residual tests: {'Yes' if ljung_box_success else 'No'}")
print(f"  ✅ Excel file with all results: {'Yes' if excel_filename else 'No'}")
print(f"  ✅ Comprehensive visualizations: {'Yes' if CREATE_PLOTS else 'No'}")
print(f"  ✅ Model diagnostic tests: {'Yes' if ljung_box_success else 'No'}")

print(f"\n💡 MODEL INTERPRETATION:")
if arch_results is not None:
    accuracy = 100 - arch_results['MAPE'].iloc[0]
    correlation = arch_results['Correlation'].iloc[0]
    
    if accuracy > 85 and correlation > 0.7:
        print(f"  🏆 EXCELLENT: ARCH({final_q}) model performs very well")
        print(f"  🏆 High accuracy ({accuracy:.1f}%) and strong correlation ({correlation:.3f})")
    elif accuracy > 75 and correlation > 0.5:
        print(f"  ✅ GOOD: ARCH({final_q}) model shows solid performance")
        print(f"  ✅ Decent accuracy ({accuracy:.1f}%) and moderate correlation ({correlation:.3f})")
    elif accuracy > 60 and correlation > 0.3:
        print(f"  ⚠️ ACCEPTABLE: ARCH({final_q}) model has modest performance")
        print(f"  ⚠️ May benefit from parameter tuning or alternative specifications")
    else:
        print(f"  ❌ POOR: ARCH({final_q}) model shows limited performance")
        print(f"  ❌ Consider different model types (GARCH, EGARCH, etc.)")

print("="*80)
print("🏛️ ARCH MODEL ANALYSIS COMPLETE - ALL RESULTS SAVED!")
print("🔄 Modify parameters above to experiment with different ARCH specifications!")

# Display available objects for further analysis
print(f"\n📋 AVAILABLE OBJECTS FOR FURTHER ANALYSIS:")
print(f"  - btc_data: Raw Bitcoin OHLCV data")
print(f"  - featured_data: Complete dataset with ARCH features")
print(f"  - fitted_arch: Trained ARCH({final_q}) model object")
print(f"  - arch_results: Detailed prediction results DataFrame")
print(f"  - ljung_box_summary: Ljung-Box test results DataFrame")
print(f"  - arch_predictions, arch_actuals: Prediction and actual series")
print(f"  - returns_train, returns_test: Training and testing return data")
print(f"  - ljung_box_tester: Ljung-Box testing object with all methods")

