# Define EnhancedBitcoinFeatureEngine class for the model downloads
class EnhancedBitcoinFeatureEngine(BitcoinFeatureEngine):
    """Enhanced version of Bitcoin Feature Engine with model-specific features"""
    
    def calculate_all_features(self):
        """Calculate all features including model-specific ones"""
        # Calculate base features using parent class
        super().calculate_all_features()
        
        # Create model-specific feature sets
        self._create_model_feature_sets()
        
        return self.df
    
    def _create_model_feature_sets(self):
        """Create model-specific feature sets"""
        print("  - Creating model-specific feature sets...")
        
        # Get all feature columns (exclude targets and basic OHLCV)
        all_features = [col for col in self.df.columns 
                       if not any(x in col for x in ['next_', 'target_', 'signal_']) 
                       and col not in ['open', 'high', 'low', 'close', 'volume']]
        
        # ARCH Model Features
        self.arch_features = [
            'log_return_1d',
            'volatility_20d',
            'return_skewness_10d'
        ]
        
        # GARCH Model Features
        self.garch_features = [
            'log_return_1d',
            'vol_of_vol',
            'volatility_ratio',
            'return_autocorr_5d'
        ]
        
        # EGARCH Model Features
        self.egarch_features = [
            'log_return_1d',
            'return_skewness_10d',
            'range_position',
            'price_vs_ma20',
            'trend_strength'
        ]
        
        # GJR-GARCH Model Features
        self.gjr_features = [
            'log_return_1d',
            'return_skewness_10d',
            'volatility_regime',
            'price_percentile_30d'
        ]
        
        # APARCH Model Features
        self.aparch_features = [
            'log_return_1d',
            'parkinson_vol',
            'return_skewness_10d',
            'vol_of_vol',
            'noise_ratio'
        ]
        
        # Price Prediction Model Features (limited subset)
        self.price_prediction_features = [
            # Core price features
            'log_return_1d', 'price_momentum_10d',
            
            # Technical indicators
            'rsi', 'bollinger_position',
            
            # Moving averages
            'price_vs_ma20', 'price_vs_ma50', 'price_vs_ma200',
            'ma20_vs_ma50', 'ma50_vs_ma200',
            
            # Volatility
            'volatility_20d', 'volatility_ratio',
            
            # Position/Momentum
            'price_percentile_30d', 'distance_from_high',
            'distance_from_low', 'return_persistence',
            
            # Advanced
            'z_score_20d', 'trend_strength',
            'hurst_exponent'
        ]
        
        # Signal Generation Model Features (limited subset)
        self.signal_prediction_features = [
            # Momentum indicators
            'price_momentum_5d', 'return_momentum_5d',
            'rsi',
            
            # Trend indicators
            'price_vs_ma20', 'ma20_vs_ma50', 'trend_strength',
            
            # Price position
            'price_percentile_30d', 'bollinger_position',
            
            # Advanced signals
            'z_score_20d', 'mean_reversion_pressure', 'return_skewness_10d',
            'return_persistence', 'trend_persistence'
        ]
        
        # Filter features to only include those that exist
        self.arch_features = [f for f in self.arch_features if f in self.df.columns]
        self.garch_features = [f for f in self.garch_features if f in self.df.columns]
        self.egarch_features = [f for f in self.egarch_features if f in self.df.columns]
        self.gjr_features = [f for f in self.gjr_features if f in self.df.columns]
        self.aparch_features = [f for f in self.aparch_features if f in self.df.columns]
        self.price_prediction_features = [f for f in self.price_prediction_features if f in self.df.columns]
        self.signal_prediction_features = [f for f in self.signal_prediction_features if f in self.df.columns]
    
    def get_model_features(self, model_type):
        """Get features for specific model type"""
        feature_map = {
            'ARCH': self.arch_features,
            'GARCH': self.garch_features,
            'EGARCH': self.egarch_features,
            'GJR-GARCH': self.gjr_features,
            'APARCH': self.aparch_features,
            'Price_Prediction': self.price_prediction_features,
            'Signal_Generation': self.signal_prediction_features
        }
        return feature_map.get(model_type, [])





import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class BitcoinFeatureEngine:
    """
    Comprehensive feature engineering for Bitcoin price prediction
    Calculates all price-based features from the documentation
    """
    
    def __init__(self, df):
        """
        Initialize with OHLCV dataframe
        Expected columns: ['open', 'high', 'low', 'close', 'volume']
        Index should be datetime
        """
        self.df = df.copy()
        self.df.columns = self.df.columns.str.lower()
        required_cols = ['open', 'high', 'low', 'close']
        
        if not all(col in self.df.columns for col in required_cols):
            raise ValueError(f"DataFrame must contain columns: {required_cols}")
    
    def calculate_all_features(self):
        """Calculate all price-based features"""
        print("Calculating Bitcoin price-based features...")
        
        # 1. Returns Features
        self._calculate_returns()
        
        # 2. Volatility Features  
        self._calculate_volatility()
        
        # 3. Moving Average Features
        self._calculate_ma_features()
        
        # 4. High-Low Range Features
        self._calculate_range_features()
        
        # 5. Price Position and Momentum
        self._calculate_position_momentum()
        
        # 6. Advanced Features
        self._calculate_advanced_features()
        
        return self.df
    
    def _calculate_returns(self):
        """Calculate return-based features"""
        print("  - Returns features...")
        
        # Basic returns
        self.df['simple_return_1d'] = self.df['close'].pct_change()
        self.df['log_return_1d'] = np.log(self.df['close'] / self.df['close'].shift(1))
        
        # Multi-period returns
        self.df['log_return_3d'] = self.df['log_return_1d'].rolling(3).sum()
        self.df['log_return_7d'] = self.df['log_return_1d'].rolling(7).sum()
        self.df['log_return_30d'] = self.df['log_return_1d'].rolling(30).sum()
        
        # Return derivatives
        self.df['return_momentum_5d'] = self.df['log_return_1d'].rolling(5).mean()
        self.df['return_acceleration'] = self.df['log_return_1d'].diff()
        
        # Return volatility ratio
        rolling_vol = self.df['log_return_1d'].rolling(20).std()
        self.df['return_volatility_ratio'] = self.df['log_return_1d'] / rolling_vol
        
        # Return skewness (KEY FEATURE)
        self.df['return_skewness_10d'] = self.df['log_return_1d'].rolling(10).apply(
            lambda x: stats.skew(x.dropna()) if len(x.dropna()) >= 3 else np.nan
        )
        
        # Return persistence (KEY FEATURE)
        self.df['return_persistence'] = self._calculate_return_persistence()
        
        # Risk-adjusted returns
        self.df['sharpe_10d'] = (self.df['return_momentum_5d'] * np.sqrt(252)) / (rolling_vol * np.sqrt(252))
        
        # Return autocorrelation
        self.df['return_autocorr_5d'] = self.df['log_return_1d'].rolling(10).apply(
            lambda x: x.autocorr(lag=1) if len(x.dropna()) > 5 else np.nan
        )
    
    def _calculate_return_persistence(self):
        """Calculate return persistence - consistency of return direction"""
        returns = self.df['log_return_1d']
        
        # Method 1: Directional consistency ratio (5-day window)
        return_signs = np.sign(returns)
        consistency = return_signs.rolling(5).apply(
            lambda x: len(x[x == x.iloc[-1]]) / len(x) if len(x) > 0 else np.nan
        )
        
        return consistency
    
    def _calculate_volatility(self):
        """Calculate volatility-based features"""
        print("  - Volatility features...")
        
        returns = self.df['log_return_1d']
        
        # Historical volatility (different windows)
        self.df['volatility_5d'] = returns.rolling(5).std() * np.sqrt(252)
        self.df['volatility_20d'] = returns.rolling(20).std() * np.sqrt(252)
        self.df['volatility_60d'] = returns.rolling(60).std() * np.sqrt(252)
        
        # Parkinson volatility (high-low based)
        try:
            self.df['parkinson_vol'] = np.sqrt(
                np.log(self.df['high'] / self.df['low']).rolling(20).apply(
                    lambda x: (x**2).mean() / (4 * np.log(2)) if len(x) > 0 else np.nan
                )
            ) * np.sqrt(252)
        except:
            self.df['parkinson_vol'] = self.df['volatility_20d']  # Fallback
        
        # Garman-Klass volatility
        try:
            self.df['garman_klass_vol'] = self._calculate_garman_klass()
        except:
            self.df['garman_klass_vol'] = self.df['volatility_20d']  # Fallback
        
        # Volatility relationships
        self.df['volatility_ratio'] = self.df['volatility_5d'] / self.df['volatility_60d']
        self.df['volatility_momentum'] = self.df['volatility_20d'].pct_change(5)
        
        # Volatility regime classification (fixed)
        vol_20d = self.df['volatility_20d']
        try:
            vol_low = vol_20d.rolling(252).quantile(0.33)
            vol_high = vol_20d.rolling(252).quantile(0.67)
            
            # Create volatility regime: 0=Low, 1=Medium, 2=High
            conditions = [
                vol_20d <= vol_low,
                (vol_20d > vol_low) & (vol_20d <= vol_high),
                vol_20d > vol_high
            ]
            self.df['volatility_regime'] = np.select(conditions, [0, 1, 2], default=np.nan)
        except:
            # Simple fallback regime classification
            vol_median = vol_20d.rolling(252).median()
            self.df['volatility_regime'] = np.where(vol_20d > vol_median, 1, 0)
        
        # Volatility mean reversion
        vol_mean = self.df['volatility_20d'].rolling(252).mean()
        self.df['volatility_mean_reversion'] = (self.df['volatility_20d'] - vol_mean) / vol_mean
        
        # Advanced volatility features
        try:
            self.df['volatility_clustering'] = self._calculate_vol_clustering()
        except:
            self.df['volatility_clustering'] = self.df['volatility_20d'].rolling(10).std()
            
        self.df['vol_of_vol'] = self.df['volatility_20d'].rolling(20).std()
    
    def _calculate_garman_klass(self):
        """Calculate Garman-Klass volatility estimator"""
        o, h, l, c = self.df['open'], self.df['high'], self.df['low'], self.df['close']
        
        term1 = 0.5 * (np.log(h/l))**2
        term2 = (2*np.log(2) - 1) * (np.log(c/o))**2
        
        gk_vol = (term1 - term2).rolling(20).mean()
        return np.sqrt(gk_vol * 252)
    
    def _calculate_vol_clustering(self):
        """Measure volatility clustering/persistence"""
        vol_changes = self.df['volatility_5d'].pct_change().abs()
        return vol_changes.rolling(10).std()
    
    def _calculate_ma_features(self):
        """Calculate moving average features"""
        print("  - Moving average features...")
        
        close = self.df['close']
        
        # Moving averages
        self.df['ma20'] = close.rolling(20).mean()
        self.df['ma50'] = close.rolling(50).mean()
        self.df['ma200'] = close.rolling(200).mean()
        
        # Distance from MAs (percentage)
        self.df['price_vs_ma20'] = (close - self.df['ma20']) / self.df['ma20'] * 100
        self.df['price_vs_ma50'] = (close - self.df['ma50']) / self.df['ma50'] * 100
        self.df['price_vs_ma200'] = (close - self.df['ma200']) / self.df['ma200'] * 100
        
        # MA relationships
        self.df['ma20_vs_ma50'] = (self.df['ma20'] - self.df['ma50']) / self.df['ma50'] * 100
        self.df['ma50_vs_ma200'] = (self.df['ma50'] - self.df['ma200']) / self.df['ma200'] * 100
        
        # MA convergence
        ma_spread = np.abs(self.df['ma20'] - self.df['ma50']) + np.abs(self.df['ma50'] - self.df['ma200'])
        self.df['ma_convergence'] = ma_spread / close
        
        # MA slopes (momentum)
        self.df['ma20_slope'] = self.df['ma20'].pct_change(5) * 100
        self.df['ma50_slope'] = self.df['ma50'].pct_change(5) * 100
        self.df['ma_acceleration'] = self.df['ma20_slope'].diff()
        
        # Binary position features
        self.df['price_above_ma20'] = (close > self.df['ma20']).astype(int)
        self.df['price_above_ma50'] = (close > self.df['ma50']).astype(int)
        self.df['price_above_all_mas'] = (
            (close > self.df['ma20']) & 
            (close > self.df['ma50']) & 
            (close > self.df['ma200'])
        ).astype(int)
        
        # Bullish alignment
        self.df['ma_bullish_alignment'] = (
            (self.df['ma20'] > self.df['ma50']) & 
            (self.df['ma50'] > self.df['ma200'])
        ).astype(int)
    
    def _calculate_range_features(self):
        """Calculate high-low range features"""
        print("  - Range features...")
        
        o, h, l, c = self.df['open'], self.df['high'], self.df['low'], self.df['close']
        prev_close = c.shift(1)
        
        # Daily range indicators
        self.df['daily_range'] = (h - l) / c * 100
        avg_range = self.df['daily_range'].rolling(20).mean()
        self.df['range_vs_average'] = self.df['daily_range'] / avg_range
        self.df['range_position'] = (c - l) / (h - l)
        self.df['gap_open'] = (o - prev_close) / prev_close * 100
        
        # True Range and ATR
        tr1 = h - l
        tr2 = np.abs(h - prev_close)
        tr3 = np.abs(l - prev_close)
        self.df['true_range'] = np.maximum(tr1, np.maximum(tr2, tr3))
        self.df['average_true_range'] = self.df['true_range'].rolling(14).mean()
        
        # Range expansion/contraction
        range_ma = self.df['daily_range'].rolling(10).mean()
        self.df['range_expansion'] = self.df['daily_range'] / range_ma
        self.df['range_contraction'] = 1 / self.df['range_expansion']
    
    def _calculate_position_momentum(self):
        """Calculate price position and momentum features"""
        print("  - Position and momentum features...")
        
        close = self.df['close']
        
        # Price percentiles
        self.df['price_percentile_30d'] = close.rolling(30).rank(pct=True) * 100
        self.df['price_percentile_90d'] = close.rolling(90).rank(pct=True) * 100
        
        # Distance from highs/lows
        rolling_high_30d = close.rolling(30).max()
        rolling_low_30d = close.rolling(30).min()
        self.df['distance_from_high'] = (rolling_high_30d - close) / close * 100
        self.df['distance_from_low'] = (close - rolling_low_30d) / close * 100
        
        # Momentum features
        self.df['price_momentum_10d'] = close.pct_change(10) * 100
        
        # RSI-style momentum
        delta = close.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(14).mean()
        avg_loss = loss.rolling(14).mean()
        rs = avg_gain / avg_loss
        self.df['rsi'] = 100 - (100 / (1 + rs))
        
        # Momentum persistence
        momentum_sign = np.sign(self.df['price_momentum_10d'])
        self.df['momentum_persistence'] = momentum_sign.rolling(5).apply(
            lambda x: (x == x.iloc[-1]).sum() / len(x) if len(x) > 0 else np.nan
        )
    
    def _calculate_advanced_features(self):
        """Calculate advanced price-based features"""
        print("  - Advanced features...")
        
        close = self.df['close']
        returns = self.df['log_return_1d']
        
        # Z-scores (mean reversion indicators)
        rolling_mean_20d = close.rolling(20).mean()
        rolling_std_20d = close.rolling(20).std()
        self.df['z_score_20d'] = (close - rolling_mean_20d) / rolling_std_20d
        
        rolling_mean_60d = close.rolling(60).mean()
        rolling_std_60d = close.rolling(60).std()
        self.df['z_score_60d'] = (close - rolling_mean_60d) / rolling_std_60d
        
        # Bollinger Bands position
        bb_upper = rolling_mean_20d + (2 * rolling_std_20d)
        bb_lower = rolling_mean_20d - (2 * rolling_std_20d)
        self.df['bollinger_position'] = (close - bb_lower) / (bb_upper - bb_lower)
        
        # Mean reversion pressure
        long_term_mean = close.rolling(252).mean()
        self.df['mean_reversion_pressure'] = (close - long_term_mean) / long_term_mean * 100
        
        # Trend quality features
        self.df['trend_strength'] = self._calculate_trend_strength()
        self.df['trend_persistence'] = self._calculate_trend_persistence()
        
        # Market microstructure approximations
        self.df['noise_ratio'] = self._calculate_noise_ratio()
        
        # Hurst exponent (trend vs mean reversion tendency)
        self.df['hurst_exponent'] = returns.rolling(50).apply(
            lambda x: self._calculate_hurst(x.dropna()) if len(x.dropna()) > 10 else np.nan
        )
    
    def _calculate_trend_strength(self):
        """Calculate trend strength based on price consistency"""
        close = self.df['close']
        trend_up = (close > close.shift(1)).rolling(10).sum() / 10
        trend_down = (close < close.shift(1)).rolling(10).sum() / 10
        return np.maximum(trend_up, trend_down)
    
    def _calculate_trend_persistence(self):
        """Calculate how long trends persist"""
        price_changes = np.sign(self.df['close'].diff())
        
        # Calculate run lengths
        runs = []
        current_run = 1
        
        for i in range(1, len(price_changes)):
            if price_changes.iloc[i] == price_changes.iloc[i-1]:
                current_run += 1
            else:
                runs.append(current_run)
                current_run = 1
        
        # Rolling average of recent run lengths
        trend_persistence = pd.Series(index=self.df.index, dtype=float)
        window = 20
        
        for i in range(window, len(self.df)):
            recent_runs = [r for r in runs if r > 0][-window:]
            if recent_runs:
                trend_persistence.iloc[i] = np.mean(recent_runs)
        
        return trend_persistence
    
    def _calculate_noise_ratio(self):
        """Estimate noise vs signal in price movements"""
        returns = self.df['log_return_1d']
        
        # Compare actual volatility to theoretical random walk
        actual_vol = returns.rolling(20).std()
        
        # Theoretical vol if purely random
        daily_returns = returns.rolling(20).apply(lambda x: np.mean(np.abs(x)))
        theoretical_vol = daily_returns * np.sqrt(np.pi/2)  # For random walk
        
        return actual_vol / theoretical_vol
    
    def _calculate_hurst(self, returns):
        """Calculate Hurst exponent for trend vs mean reversion"""
        if len(returns) < 10:
            return np.nan
            
        try:
            # Simple R/S calculation
            n = len(returns)
            mean_return = np.mean(returns)
            
            # Cumulative deviations
            cum_devs = np.cumsum(returns - mean_return)
            
            # Range
            R = np.max(cum_devs) - np.min(cum_devs)
            
            # Standard deviation
            S = np.std(returns)
            
            if S == 0:
                return 0.5
                
            # R/S ratio
            rs_ratio = R / S
            
            # Hurst exponent approximation
            if rs_ratio > 0:
                return np.log(rs_ratio) / np.log(n)
            else:
                return 0.5
                
        except:
            return np.nan
    
    def get_tier1_features(self):
        """Return Tier 1 (essential) features"""
        tier1_features = [
            'log_return_1d',
            'volatility_20d', 
            'price_vs_ma20',
            'return_momentum_5d',
            'daily_range'
        ]
        
        available_features = [f for f in tier1_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_tier2_features(self):
        """Return Tier 2 (high value) features"""
        tier2_features = [
            'volatility_ratio',
            'price_vs_ma50',
            'ma20_vs_ma50', 
            'price_percentile_30d',
            'return_acceleration'
        ]
        
        available_features = [f for f in tier2_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_tier3_features(self):
        """Return Tier 3 (advanced) features"""
        tier3_features = [
            'z_score_20d',
            'trend_persistence',
            'volatility_momentum',
            'return_skewness_10d',
            'return_persistence'
        ]
        
        available_features = [f for f in tier3_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_feature_summary(self):
        """Get summary of all calculated features"""
        feature_cols = [col for col in self.df.columns 
                       if col not in ['open', 'high', 'low', 'close', 'volume']]
        
        summary = {}
        for col in feature_cols:
            summary[col] = {
                'count': self.df[col].count(),
                'mean': self.df[col].mean(),
                'std': self.df[col].std(),
                'min': self.df[col].min(),
                'max': self.df[col].max()
            }
        
        return pd.DataFrame(summary).T


# Bitcoin Data Fetcher
def fetch_bitcoin_data(start_date='2020-01-01', end_date=None, period='max'):
    """
    Fetch Bitcoin data from Yahoo Finance
    
    Parameters:
    start_date (str): Start date in 'YYYY-MM-DD' format
    end_date (str): End date in 'YYYY-MM-DD' format (None for today)
    period (str): Period for data ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
    
    Returns:
    pandas.DataFrame: Bitcoin OHLCV data
    """
    try:
        import yfinance as yf
        print("Fetching Bitcoin data from Yahoo Finance...")
        
        # Bitcoin ticker symbol
        btc_ticker = "BTC-USD"
        
        # Create ticker object
        btc = yf.Ticker(btc_ticker)
        
        # Fetch historical data
        if start_date and end_date:
            btc_data = btc.history(start=start_date, end=end_date, interval='1d')
        elif start_date:
            btc_data = btc.history(start=start_date, interval='1d')
        else:
            btc_data = btc.history(period=period, interval='1d')
        
        if btc_data.empty:
            raise ValueError("No data retrieved. Check your date range or internet connection.")
        
        # Clean column names and select required columns
        btc_data.columns = btc_data.columns.str.lower()
        btc_data = btc_data[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # Remove timezone from datetime index (Excel compatibility)
        if btc_data.index.tz is not None:
            btc_data.index = btc_data.index.tz_localize(None)
        
        # Remove any rows with missing data
        btc_data = btc_data.dropna()
        
        print(f"✅ Successfully fetched {len(btc_data)} days of Bitcoin data")
        print(f"📅 Date range: {btc_data.index.min().date()} to {btc_data.index.max().date()}")
        print(f"💰 Price range: ${btc_data['close'].min():.2f} to ${btc_data['close'].max():.2f}")
        
        return btc_data
        
    except ImportError:
        print("❌ yfinance not installed. Installing now...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "yfinance"])
        
        # Try again after installation
        import yfinance as yf
        return fetch_bitcoin_data(start_date, end_date, period)
        
    except Exception as e:
        print(f"❌ Error fetching Bitcoin data: {e}")
        print("Using sample data instead...")
        return create_sample_data()

def create_sample_data():
    """Create sample Bitcoin data as fallback"""
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', '2024-01-01', freq='D')
    
    # Simulate realistic Bitcoin price data
    initial_price = 10000
    returns = np.random.normal(0.001, 0.04, len(dates))
    prices = [initial_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Create OHLCV data
    bitcoin_data = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.02))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.02))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(1000, 10000, len(dates))
    }, index=dates)
    
    # Ensure high >= close >= low and high >= open >= low
    bitcoin_data['high'] = bitcoin_data[['open', 'close', 'high']].max(axis=1)
    bitcoin_data['low'] = bitcoin_data[['open', 'close', 'low']].min(axis=1)
    
    return bitcoin_data

# Example usage and testing
def demo_usage(use_real_data=True, start_date='2020-01-01', end_date=None):
    """
    Demonstrate how to use the feature engine with real Bitcoin data
    
    Parameters:
    use_real_data (bool): Whether to fetch real data from Yahoo Finance
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    """
    
    if use_real_data:
        # Fetch real Bitcoin data from Yahoo Finance
        bitcoin_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    else:
        # Use sample data
        bitcoin_data = create_sample_data()
        print("Using sample Bitcoin data")
    
    print("\n📊 Bitcoin OHLCV Data Preview:")
    print(bitcoin_data.head())
    print(f"\n📈 Data shape: {bitcoin_data.shape}")
    
    # Basic statistics
    print(f"\n💹 Current Bitcoin Price: ${bitcoin_data['close'].iloc[-1]:,.2f}")
    print(f"📊 30-day return: {((bitcoin_data['close'].iloc[-1] / bitcoin_data['close'].iloc[-30]) - 1) * 100:.2f}%")
    print(f"📊 YTD return: {((bitcoin_data['close'].iloc[-1] / bitcoin_data['close'].iloc[0]) - 1) * 100:.2f}%")
    
    # Initialize feature engine
    print(f"\n🔧 Initializing Bitcoin Feature Engine...")
    feature_engine = BitcoinFeatureEngine(bitcoin_data)
    
    # Calculate all features
    featured_data = feature_engine.calculate_all_features()
    
    print(f"\n✅ Features calculated! New shape: {featured_data.shape}")
    print(f"📊 Total features created: {featured_data.shape[1] - 5}")  # Subtract OHLCV columns
    
    # Show key features
    print("\n" + "="*50)
    print("🎯 KEY FEATURES ANALYSIS")
    print("="*50)
    
    print("\n📈 Return Skewness (10-day) - Last 10 days:")
    skew_data = featured_data['return_skewness_10d'].tail(10)
    for date, value in skew_data.items():
        if not pd.isna(value):
            interpretation = "📉 Crash risk" if value < -0.5 else "📈 Upside bias" if value > 0.5 else "⚖️ Balanced"
            print(f"  {date.date()}: {value:.3f} {interpretation}")
    
    print("\n🔄 Return Persistence - Last 10 days:")
    persist_data = featured_data['return_persistence'].tail(10)
    for date, value in persist_data.items():
        if not pd.isna(value):
            interpretation = "🚀 Strong trend" if value > 0.8 else "📊 Choppy" if value < 0.6 else "📈 Moderate trend"
            print(f"  {date.date()}: {value:.3f} {interpretation}")
    
    # Current market conditions
    latest = featured_data.iloc[-1]
    print(f"\n🔍 CURRENT MARKET CONDITIONS:")
    print(f"💰 Price vs 20-day MA: {latest['price_vs_ma20']:.2f}%")
    print(f"📊 20-day Volatility: {latest['volatility_20d']:.1f}%")
    print(f"📈 10-day Momentum: {latest['price_momentum_10d']:.2f}%")
    print(f"🎯 30-day Price Percentile: {latest['price_percentile_30d']:.1f}%")
    
    # Get feature tiers
    print("\n" + "="*50)
    print("📊 TIER 1 FEATURES (Essential)")
    print("="*50)
    tier1 = feature_engine.get_tier1_features()
    print(tier1.tail(3))
    
    print("\n" + "="*50)
    print("📈 TIER 2 FEATURES (High Value)")
    print("="*50)
    tier2 = feature_engine.get_tier2_features()
    print(tier2.tail(3))
    
    print("\n" + "="*50)
    print("🔬 TIER 3 FEATURES (Advanced)")
    print("="*50)
    tier3 = feature_engine.get_tier3_features()
    print(tier3.tail(3))
    
    # Feature correlation analysis
    print("\n📊 Feature Correlation with Future Returns:")
    future_return = featured_data['log_return_1d'].shift(-1)  # Next day return
    
    key_features = ['return_skewness_10d', 'return_persistence', 'volatility_ratio', 
                   'price_vs_ma20', 'z_score_20d']
    
    correlations = {}
    for feature in key_features:
        if feature in featured_data.columns:
            corr = featured_data[feature].corr(future_return)
            correlations[feature] = corr
            print(f"  {feature}: {corr:.3f}")
    
    return featured_data, feature_engine

# Quick analysis function
def quick_bitcoin_analysis(days_back=30):
    """Quick Bitcoin analysis for recent data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.Timedelta(days=days_back + 200)  # Extra days for feature calculation
    
    btc_data = fetch_bitcoin_data(start_date=start_date.strftime('%Y-%m-%d'))
    engine = BitcoinFeatureEngine(btc_data)
    featured_data = engine.calculate_all_features()
    
    # Focus on recent data
    recent_data = featured_data.tail(days_back)
    
    print(f"\n🔍 BITCOIN ANALYSIS - Last {days_back} Days")
    print("="*50)
    
    latest = recent_data.iloc[-1]
    
    print(f"💰 Current Price: ${latest['close']:,.2f}")
    print(f"📊 Daily Return: {latest['log_return_1d']*100:.2f}%")
    print(f"📈 Return Skewness: {latest['return_skewness_10d']:.3f}")
    print(f"🔄 Return Persistence: {latest['return_persistence']:.3f}")
    print(f"⚡ Volatility Regime: {latest['volatility_regime']:.0f} (0=Low, 1=Med, 2=High)")
    print(f"🎯 Trend Strength: {latest['trend_strength']:.3f}")
    
    return recent_data

# Excel Export Functions
def save_to_excel(featured_data, filename=None, include_summary=True):
    """
    Save Bitcoin data with features to Excel file
    
    Parameters:
    featured_data (pd.DataFrame): DataFrame with all features
    filename (str): Output filename (None for auto-generated name)
    include_summary (bool): Whether to include summary sheet
    
    Returns:
    str: Path to saved file
    """
    try:
        if filename is None:
            # Auto-generate filename with current date
            current_date = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            filename = f'bitcoin_features_{current_date}.xlsx'
        
        print(f"💾 Saving Bitcoin data to Excel: {filename}")
        
        # Remove timezone from index if present (Excel compatibility)
        if featured_data.index.tz is not None:
            featured_data = featured_data.copy()
            featured_data.index = featured_data.index.tz_localize(None)
        
        # Create Excel writer
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            
            # Main data sheet
            featured_data.to_excel(writer, sheet_name='Bitcoin_Features', index=True)
            print("✅ Main data saved to 'Bitcoin_Features' sheet")
            
            if include_summary:
                # Create summary sheet
                summary_data = create_excel_summary(featured_data)
                
                # Feature summary - create a simple summary instead of using feature engine
                feature_cols = [col for col in featured_data.columns 
                               if col not in ['open', 'high', 'low', 'close', 'volume']]
                
                feature_summary = pd.DataFrame({
                    'count': featured_data[feature_cols].count(),
                    'mean': featured_data[feature_cols].mean(),
                    'std': featured_data[feature_cols].std(),
                    'min': featured_data[feature_cols].min(),
                    'max': featured_data[feature_cols].max()
                })
                
                feature_summary.to_excel(writer, sheet_name='Feature_Summary')
                print("✅ Feature summary saved to 'Feature_Summary' sheet")
                
                # Tier features
                tier1_features = get_tier_features(featured_data, tier=1)
                tier1_features.to_excel(writer, sheet_name='Tier1_Essential')
                print("✅ Tier 1 features saved to 'Tier1_Essential' sheet")
                
                tier2_features = get_tier_features(featured_data, tier=2)
                tier2_features.to_excel(writer, sheet_name='Tier2_HighValue')
                print("✅ Tier 2 features saved to 'Tier2_HighValue' sheet")
                
                tier3_features = get_tier_features(featured_data, tier=3)
                tier3_features.to_excel(writer, sheet_name='Tier3_Advanced')
                print("✅ Tier 3 features saved to 'Tier3_Advanced' sheet")
                
                # Market analysis
                market_analysis = create_market_analysis(featured_data)
                market_analysis.to_excel(writer, sheet_name='Market_Analysis', index=False)
                print("✅ Market analysis saved to 'Market_Analysis' sheet")
        
        print(f"\n🎉 Successfully saved Bitcoin data to: {filename}")
        print(f"📊 Total rows: {len(featured_data)}")
        print(f"📈 Total features: {len(featured_data.columns)}")
        print(f"📅 Date range: {featured_data.index.min().date()} to {featured_data.index.max().date()}")
        
        return filename
        
    except Exception as e:
        print(f"❌ Error saving to Excel: {e}")
        return None

def create_excel_summary(featured_data):
    """Create summary statistics for Excel export"""
    latest = featured_data.iloc[-1]
    
    summary = {
        'Metric': [
            'Current Price',
            'Daily Return (%)',
            '30-day Return (%)',
            'YTD Return (%)',
            '20-day Volatility (%)',
            'Return Skewness (10d)',
            'Return Persistence',
            'Price vs MA20 (%)',
            'Price vs MA50 (%)',
            'Volatility Regime',
            'Trend Strength',
            'RSI',
            '30-day Price Percentile',
            'Z-Score (20d)'
        ],
        'Value': [
            f"${latest['close']:,.2f}",
            f"{latest['log_return_1d']*100:.2f}%",
            f"{((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%",
            f"{((featured_data['close'].iloc[-1] / featured_data['close'].iloc[0]) - 1) * 100:.2f}%",
            f"{latest['volatility_20d']:.1f}%",
            f"{latest['return_skewness_10d']:.3f}",
            f"{latest['return_persistence']:.3f}",
            f"{latest['price_vs_ma20']:.2f}%",
            f"{latest['price_vs_ma50']:.2f}%",
            f"{latest['volatility_regime']:.0f}",
            f"{latest['trend_strength']:.3f}",
            f"{latest['rsi']:.1f}",
            f"{latest['price_percentile_30d']:.1f}%",
            f"{latest['z_score_20d']:.3f}"
        ],
        'Interpretation': [
            'Current Bitcoin price',
            'Today\'s price change',
            'Monthly performance',
            'Year-to-date performance',
            'Risk/uncertainty level',
            'Asymmetry in returns',
            'Directional consistency',
            'Short-term trend position',
            'Medium-term trend position',
            '0=Low, 1=Med, 2=High vol',
            'Trend consistency (0-1)',
            'Momentum indicator (0-100)',
            'Relative price position',
            'Distance from mean'
        ]
    }
    
    return pd.DataFrame(summary)

def get_tier_features(featured_data, tier=1):
    """Get specific tier features for Excel export"""
    if tier == 1:
        features = ['log_return_1d', 'volatility_20d', 'price_vs_ma20', 'return_momentum_5d', 'daily_range']
    elif tier == 2:
        features = ['volatility_ratio', 'price_vs_ma50', 'ma20_vs_ma50', 'price_percentile_30d', 'return_acceleration']
    elif tier == 3:
        features = ['z_score_20d', 'trend_persistence', 'volatility_momentum', 'return_skewness_10d', 'return_persistence']
    else:
        return pd.DataFrame()
    
    available_features = [f for f in features if f in featured_data.columns]
    return featured_data[available_features]

def create_market_analysis(featured_data):
    """Create market analysis summary for Excel"""
    latest = featured_data.iloc[-1]
    
    # Market condition interpretations
    analysis = []
    
    # Price trend analysis
    if latest['price_vs_ma20'] > 5:
        trend_analysis = "Strong uptrend - Price well above 20-day MA"
    elif latest['price_vs_ma20'] > 0:
        trend_analysis = "Mild uptrend - Price above 20-day MA"
    elif latest['price_vs_ma20'] > -5:
        trend_analysis = "Mild downtrend - Price below 20-day MA"
    else:
        trend_analysis = "Strong downtrend - Price well below 20-day MA"
    
    # Volatility analysis
    if latest['volatility_regime'] == 2:
        vol_analysis = "High volatility environment - Expect large price swings"
    elif latest['volatility_regime'] == 1:
        vol_analysis = "Medium volatility - Normal market conditions"
    else:
        vol_analysis = "Low volatility - Calm market conditions"
    
    # Momentum analysis
    if latest['return_persistence'] > 0.8:
        momentum_analysis = "Strong momentum - Trend likely to continue"
    elif latest['return_persistence'] > 0.6:
        momentum_analysis = "Moderate momentum - Some directional bias"
    else:
        momentum_analysis = "Weak momentum - Choppy/sideways movement"
    
    # Risk analysis
    if latest['return_skewness_10d'] < -0.5:
        risk_analysis = "Elevated crash risk - Recent negative skew in returns"
    elif latest['return_skewness_10d'] > 0.5:
        risk_analysis = "Upside bias - Recent positive skew in returns"
    else:
        risk_analysis = "Balanced risk - Symmetric return distribution"
    
    analysis = [
        {'Category': 'Price Trend', 'Analysis': trend_analysis, 'Value': f"{latest['price_vs_ma20']:.2f}%"},
        {'Category': 'Volatility', 'Analysis': vol_analysis, 'Value': f"{latest['volatility_20d']:.1f}%"},
        {'Category': 'Momentum', 'Analysis': momentum_analysis, 'Value': f"{latest['return_persistence']:.3f}"},
        {'Category': 'Risk Profile', 'Analysis': risk_analysis, 'Value': f"{latest['return_skewness_10d']:.3f}"},
    ]
    
    return pd.DataFrame(analysis)

# Complete workflow function
def download_bitcoin_excel(start_date='2020-01-01', end_date=None, filename=None):
    """
    Complete workflow: Fetch Bitcoin data, calculate features, save to Excel
    
    Parameters:
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    filename (str): Output filename (None for auto-generated)
    
    Returns:
    tuple: (featured_data, filename)
    """
    print("🚀 Starting Bitcoin Data Download & Feature Engineering...")
    print("="*60)
    
    # Step 1: Fetch Bitcoin data
    print("📊 Step 1: Fetching Bitcoin data from Yahoo Finance...")
    btc_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    
    if btc_data is None or btc_data.empty:
        print("❌ Failed to fetch Bitcoin data")
        return None, None
    
    # Step 2: Calculate features
    print("\n🔧 Step 2: Calculating all price-based features...")
    try:
        feature_engine = BitcoinFeatureEngine(btc_data)
        featured_data = feature_engine.calculate_all_features()
        
        # Ensure no timezone issues
        if featured_data.index.tz is not None:
            featured_data.index = featured_data.index.tz_localize(None)
            
    except Exception as e:
        print(f"❌ Error calculating features: {e}")
        return None, None
    
    # Step 3: Save to Excel
    print(f"\n💾 Step 3: Saving to Excel...")
    saved_filename = save_to_excel(featured_data, filename=filename, include_summary=True)
    
    if saved_filename:
        print(f"\n✅ COMPLETE! Bitcoin data saved to: {saved_filename}")
        
        # Show quick summary
        print(f"\n📋 QUICK SUMMARY:")
        print(f"📅 Data Period: {featured_data.index.min().date()} to {featured_data.index.max().date()}")
        print(f"📊 Total Records: {len(featured_data):,}")
        print(f"🔢 Total Features: {len(featured_data.columns)}")
        print(f"💰 Current Price: ${featured_data['close'].iloc[-1]:,.2f}")
        print(f"📈 30-day Return: {((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%")
        
        return featured_data, saved_filename
    else:
        return featured_data, None

# Quick download functions for different periods
def download_bitcoin_1year(filename=None):
    """Download last 1 year of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=1)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_1year.xlsx'
    )

def download_bitcoin_2years(filename=None):
    """Download last 2 years of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=2)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_2years.xlsx'
    )

def download_bitcoin_5years(filename=None):
    """Download last 5 years of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=5)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_5years.xlsx'
    )

def download_bitcoin_max(filename=None):
    """Download maximum available Bitcoin data"""
    return download_bitcoin_excel(
        start_date='2010-01-01',
        filename=filename or 'bitcoin_max_data.xlsx'
    )

if __name__ == "__main__":
    # Example usage - Download Bitcoin data to Excel
    print("🚀 Bitcoin Feature Engineering & Excel Export")
    print("=" * 50)
    
    # Option 1: Download last 2 years with all features
    print("\n📊 Downloading last 2 years of Bitcoin data...")
    data, filename = download_bitcoin_2years()
    
    # Option 2: Custom date range
    # data, filename = download_bitcoin_excel(
    #     start_date='2023-01-01', 
    #     end_date='2024-12-31',
    #     filename='bitcoin_2023_2024.xlsx'
    # )
    
    # Option 3: Maximum data available
    # data, filename = download_bitcoin_max()
    
    if filename:
        print(f"\n🎉 Success! Check your file: {filename}")
    else:
        print("\n❌ Download failed!")

import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class BitcoinFeatureEngine:
    """
    Comprehensive feature engineering for Bitcoin price prediction
    Calculates all price-based features from the documentation
    """
    
    def __init__(self, df):
        """
        Initialize with OHLCV dataframe
        Expected columns: ['open', 'high', 'low', 'close', 'volume']
        Index should be datetime
        """
        self.df = df.copy()
        self.df.columns = self.df.columns.str.lower()
        required_cols = ['open', 'high', 'low', 'close']
        
        if not all(col in self.df.columns for col in required_cols):
            raise ValueError(f"DataFrame must contain columns: {required_cols}")
    
    def calculate_all_features(self):
        """Calculate all price-based features"""
        print("Calculating Bitcoin price-based features...")
        
        # 1. Returns Features
        self._calculate_returns()
        
        # 2. Volatility Features  
        self._calculate_volatility()
        
        # 3. Moving Average Features
        self._calculate_ma_features()
        
        # 4. High-Low Range Features
        self._calculate_range_features()
        
        # 5. Price Position and Momentum
        self._calculate_position_momentum()
        
        # 6. Advanced Features
        self._calculate_advanced_features()
        
        return self.df
    
    def _calculate_returns(self):
        """Calculate return-based features"""
        print("  - Returns features...")
        
        # Basic returns
        self.df['simple_return_1d'] = self.df['close'].pct_change()
        self.df['log_return_1d'] = np.log(self.df['close'] / self.df['close'].shift(1))
        
        # Multi-period returns
        self.df['log_return_3d'] = self.df['log_return_1d'].rolling(3).sum()
        self.df['log_return_7d'] = self.df['log_return_1d'].rolling(7).sum()
        self.df['log_return_30d'] = self.df['log_return_1d'].rolling(30).sum()
        
        # Return derivatives
        self.df['return_momentum_5d'] = self.df['log_return_1d'].rolling(5).mean()
        self.df['return_acceleration'] = self.df['log_return_1d'].diff()
        
        # Return volatility ratio
        rolling_vol = self.df['log_return_1d'].rolling(20).std()
        self.df['return_volatility_ratio'] = self.df['log_return_1d'] / rolling_vol
        
        # Return skewness (KEY FEATURE)
        self.df['return_skewness_10d'] = self.df['log_return_1d'].rolling(10).apply(
            lambda x: stats.skew(x.dropna()) if len(x.dropna()) >= 3 else np.nan
        )
        
        # Return persistence (KEY FEATURE)
        self.df['return_persistence'] = self._calculate_return_persistence()
        
        # Risk-adjusted returns
        self.df['sharpe_10d'] = (self.df['return_momentum_5d'] * np.sqrt(252)) / (rolling_vol * np.sqrt(252))
        
        # Return autocorrelation
        self.df['return_autocorr_5d'] = self.df['log_return_1d'].rolling(10).apply(
            lambda x: x.autocorr(lag=1) if len(x.dropna()) > 5 else np.nan
        )
    
    def _calculate_return_persistence(self):
        """Calculate return persistence - consistency of return direction"""
        returns = self.df['log_return_1d']
        
        # Method 1: Directional consistency ratio (5-day window)
        return_signs = np.sign(returns)
        consistency = return_signs.rolling(5).apply(
            lambda x: len(x[x == x.iloc[-1]]) / len(x) if len(x) > 0 else np.nan
        )
        
        return consistency
    
    def _calculate_volatility(self):
        """Calculate volatility-based features"""
        print("  - Volatility features...")
        
        returns = self.df['log_return_1d']
        
        # Historical volatility (different windows)
        self.df['volatility_5d'] = returns.rolling(5).std() * np.sqrt(252)
        self.df['volatility_20d'] = returns.rolling(20).std() * np.sqrt(252)
        self.df['volatility_60d'] = returns.rolling(60).std() * np.sqrt(252)
        
        # Parkinson volatility (high-low based)
        try:
            self.df['parkinson_vol'] = np.sqrt(
                np.log(self.df['high'] / self.df['low']).rolling(20).apply(
                    lambda x: (x**2).mean() / (4 * np.log(2)) if len(x) > 0 else np.nan
                )
            ) * np.sqrt(252)
        except:
            self.df['parkinson_vol'] = self.df['volatility_20d']  # Fallback
        
        # Garman-Klass volatility
        try:
            self.df['garman_klass_vol'] = self._calculate_garman_klass()
        except:
            self.df['garman_klass_vol'] = self.df['volatility_20d']  # Fallback
        
        # Volatility relationships
        self.df['volatility_ratio'] = self.df['volatility_5d'] / self.df['volatility_60d']
        self.df['volatility_momentum'] = self.df['volatility_20d'].pct_change(5)
        
        # Volatility regime classification (fixed)
        vol_20d = self.df['volatility_20d']
        try:
            vol_low = vol_20d.rolling(252).quantile(0.33)
            vol_high = vol_20d.rolling(252).quantile(0.67)
            
            # Create volatility regime: 0=Low, 1=Medium, 2=High
            conditions = [
                vol_20d <= vol_low,
                (vol_20d > vol_low) & (vol_20d <= vol_high),
                vol_20d > vol_high
            ]
            self.df['volatility_regime'] = np.select(conditions, [0, 1, 2], default=np.nan)
        except:
            # Simple fallback regime classification
            vol_median = vol_20d.rolling(252).median()
            self.df['volatility_regime'] = np.where(vol_20d > vol_median, 1, 0)
        
        # Volatility mean reversion
        vol_mean = self.df['volatility_20d'].rolling(252).mean()
        self.df['volatility_mean_reversion'] = (self.df['volatility_20d'] - vol_mean) / vol_mean
        
        # Advanced volatility features
        try:
            self.df['volatility_clustering'] = self._calculate_vol_clustering()
        except:
            self.df['volatility_clustering'] = self.df['volatility_20d'].rolling(10).std()
            
        self.df['vol_of_vol'] = self.df['volatility_20d'].rolling(20).std()
    
    def _calculate_garman_klass(self):
        """Calculate Garman-Klass volatility estimator"""
        o, h, l, c = self.df['open'], self.df['high'], self.df['low'], self.df['close']
        
        term1 = 0.5 * (np.log(h/l))**2
        term2 = (2*np.log(2) - 1) * (np.log(c/o))**2
        
        gk_vol = (term1 - term2).rolling(20).mean()
        return np.sqrt(gk_vol * 252)
    
    def _calculate_vol_clustering(self):
        """Measure volatility clustering/persistence"""
        vol_changes = self.df['volatility_5d'].pct_change().abs()
        return vol_changes.rolling(10).std()
    
    def _calculate_ma_features(self):
        """Calculate moving average features"""
        print("  - Moving average features...")
        
        close = self.df['close']
        
        # Moving averages
        self.df['ma20'] = close.rolling(20).mean()
        self.df['ma50'] = close.rolling(50).mean()
        self.df['ma200'] = close.rolling(200).mean()
        
        # Distance from MAs (percentage)
        self.df['price_vs_ma20'] = (close - self.df['ma20']) / self.df['ma20'] * 100
        self.df['price_vs_ma50'] = (close - self.df['ma50']) / self.df['ma50'] * 100
        self.df['price_vs_ma200'] = (close - self.df['ma200']) / self.df['ma200'] * 100
        
        # MA relationships
        self.df['ma20_vs_ma50'] = (self.df['ma20'] - self.df['ma50']) / self.df['ma50'] * 100
        self.df['ma50_vs_ma200'] = (self.df['ma50'] - self.df['ma200']) / self.df['ma200'] * 100
        
        # MA convergence
        ma_spread = np.abs(self.df['ma20'] - self.df['ma50']) + np.abs(self.df['ma50'] - self.df['ma200'])
        self.df['ma_convergence'] = ma_spread / close
        
        # MA slopes (momentum)
        self.df['ma20_slope'] = self.df['ma20'].pct_change(5) * 100
        self.df['ma50_slope'] = self.df['ma50'].pct_change(5) * 100
        self.df['ma_acceleration'] = self.df['ma20_slope'].diff()
        
        # Binary position features
        self.df['price_above_ma20'] = (close > self.df['ma20']).astype(int)
        self.df['price_above_ma50'] = (close > self.df['ma50']).astype(int)
        self.df['price_above_all_mas'] = (
            (close > self.df['ma20']) & 
            (close > self.df['ma50']) & 
            (close > self.df['ma200'])
        ).astype(int)
        
        # Bullish alignment
        self.df['ma_bullish_alignment'] = (
            (self.df['ma20'] > self.df['ma50']) & 
            (self.df['ma50'] > self.df['ma200'])
        ).astype(int)
    
    def _calculate_range_features(self):
        """Calculate high-low range features"""
        print("  - Range features...")
        
        o, h, l, c = self.df['open'], self.df['high'], self.df['low'], self.df['close']
        prev_close = c.shift(1)
        
        # Daily range indicators
        self.df['daily_range'] = (h - l) / c * 100
        avg_range = self.df['daily_range'].rolling(20).mean()
        self.df['range_vs_average'] = self.df['daily_range'] / avg_range
        self.df['range_position'] = (c - l) / (h - l)
        self.df['gap_open'] = (o - prev_close) / prev_close * 100
        
        # True Range and ATR
        tr1 = h - l
        tr2 = np.abs(h - prev_close)
        tr3 = np.abs(l - prev_close)
        self.df['true_range'] = np.maximum(tr1, np.maximum(tr2, tr3))
        self.df['average_true_range'] = self.df['true_range'].rolling(14).mean()
        
        # Range expansion/contraction
        range_ma = self.df['daily_range'].rolling(10).mean()
        self.df['range_expansion'] = self.df['daily_range'] / range_ma
        self.df['range_contraction'] = 1 / self.df['range_expansion']
    
    def _calculate_position_momentum(self):
        """Calculate price position and momentum features"""
        print("  - Position and momentum features...")
        
        close = self.df['close']
        
        # Price percentiles
        self.df['price_percentile_30d'] = close.rolling(30).rank(pct=True) * 100
        self.df['price_percentile_90d'] = close.rolling(90).rank(pct=True) * 100
        
        # Distance from highs/lows
        rolling_high_30d = close.rolling(30).max()
        rolling_low_30d = close.rolling(30).min()
        self.df['distance_from_high'] = (rolling_high_30d - close) / close * 100
        self.df['distance_from_low'] = (close - rolling_low_30d) / close * 100
        
        # Momentum features
        self.df['price_momentum_10d'] = close.pct_change(10) * 100
        
        # RSI-style momentum
        delta = close.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(14).mean()
        avg_loss = loss.rolling(14).mean()
        rs = avg_gain / avg_loss
        self.df['rsi'] = 100 - (100 / (1 + rs))
        
        # Momentum persistence
        momentum_sign = np.sign(self.df['price_momentum_10d'])
        self.df['momentum_persistence'] = momentum_sign.rolling(5).apply(
            lambda x: (x == x.iloc[-1]).sum() / len(x) if len(x) > 0 else np.nan
        )
    
    def _calculate_advanced_features(self):
        """Calculate advanced price-based features"""
        print("  - Advanced features...")
        
        close = self.df['close']
        returns = self.df['log_return_1d']
        
        # Z-scores (mean reversion indicators)
        rolling_mean_20d = close.rolling(20).mean()
        rolling_std_20d = close.rolling(20).std()
        self.df['z_score_20d'] = (close - rolling_mean_20d) / rolling_std_20d
        
        rolling_mean_60d = close.rolling(60).mean()
        rolling_std_60d = close.rolling(60).std()
        self.df['z_score_60d'] = (close - rolling_mean_60d) / rolling_std_60d
        
        # Bollinger Bands position
        bb_upper = rolling_mean_20d + (2 * rolling_std_20d)
        bb_lower = rolling_mean_20d - (2 * rolling_std_20d)
        self.df['bollinger_position'] = (close - bb_lower) / (bb_upper - bb_lower)
        
        # Mean reversion pressure
        long_term_mean = close.rolling(252).mean()
        self.df['mean_reversion_pressure'] = (close - long_term_mean) / long_term_mean * 100
        
        # Trend quality features
        self.df['trend_strength'] = self._calculate_trend_strength()
        self.df['trend_persistence'] = self._calculate_trend_persistence()
        
        # Market microstructure approximations
        self.df['noise_ratio'] = self._calculate_noise_ratio()
        
        # Hurst exponent (trend vs mean reversion tendency)
        self.df['hurst_exponent'] = returns.rolling(50).apply(
            lambda x: self._calculate_hurst(x.dropna()) if len(x.dropna()) > 10 else np.nan
        )
    
    def _calculate_trend_strength(self):
        """Calculate trend strength based on price consistency"""
        close = self.df['close']
        trend_up = (close > close.shift(1)).rolling(10).sum() / 10
        trend_down = (close < close.shift(1)).rolling(10).sum() / 10
        return np.maximum(trend_up, trend_down)
    
    def _calculate_trend_persistence(self):
        """Calculate how long trends persist"""
        price_changes = np.sign(self.df['close'].diff())
        
        # Calculate run lengths
        runs = []
        current_run = 1
        
        for i in range(1, len(price_changes)):
            if price_changes.iloc[i] == price_changes.iloc[i-1]:
                current_run += 1
            else:
                runs.append(current_run)
                current_run = 1
        
        # Rolling average of recent run lengths
        trend_persistence = pd.Series(index=self.df.index, dtype=float)
        window = 20
        
        for i in range(window, len(self.df)):
            recent_runs = [r for r in runs if r > 0][-window:]
            if recent_runs:
                trend_persistence.iloc[i] = np.mean(recent_runs)
        
        return trend_persistence
    
    def _calculate_noise_ratio(self):
        """Estimate noise vs signal in price movements"""
        returns = self.df['log_return_1d']
        
        # Compare actual volatility to theoretical random walk
        actual_vol = returns.rolling(20).std()
        
        # Theoretical vol if purely random
        daily_returns = returns.rolling(20).apply(lambda x: np.mean(np.abs(x)))
        theoretical_vol = daily_returns * np.sqrt(np.pi/2)  # For random walk
        
        return actual_vol / theoretical_vol
    
    def _calculate_hurst(self, returns):
        """Calculate Hurst exponent for trend vs mean reversion"""
        if len(returns) < 10:
            return np.nan
            
        try:
            # Simple R/S calculation
            n = len(returns)
            mean_return = np.mean(returns)
            
            # Cumulative deviations
            cum_devs = np.cumsum(returns - mean_return)
            
            # Range
            R = np.max(cum_devs) - np.min(cum_devs)
            
            # Standard deviation
            S = np.std(returns)
            
            if S == 0:
                return 0.5
                
            # R/S ratio
            rs_ratio = R / S
            
            # Hurst exponent approximation
            if rs_ratio > 0:
                return np.log(rs_ratio) / np.log(n)
            else:
                return 0.5
                
        except:
            return np.nan
    
    def get_tier1_features(self):
        """Return Tier 1 (essential) features"""
        tier1_features = [
            'log_return_1d',
            'volatility_20d', 
            'price_vs_ma20',
            'return_momentum_5d',
            'daily_range'
        ]
        
        available_features = [f for f in tier1_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_tier2_features(self):
        """Return Tier 2 (high value) features"""
        tier2_features = [
            'volatility_ratio',
            'price_vs_ma50',
            'ma20_vs_ma50', 
            'price_percentile_30d',
            'return_acceleration'
        ]
        
        available_features = [f for f in tier2_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_tier3_features(self):
        """Return Tier 3 (advanced) features"""
        tier3_features = [
            'z_score_20d',
            'trend_persistence',
            'volatility_momentum',
            'return_skewness_10d',
            'return_persistence'
        ]
        
        available_features = [f for f in tier3_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_feature_summary(self):
        """Get summary of all calculated features"""
        feature_cols = [col for col in self.df.columns 
                       if col not in ['open', 'high', 'low', 'close', 'volume']]
        
        summary = {}
        for col in feature_cols:
            summary[col] = {
                'count': self.df[col].count(),
                'mean': self.df[col].mean(),
                'std': self.df[col].std(),
                'min': self.df[col].min(),
                'max': self.df[col].max()
            }
        
        return pd.DataFrame(summary).T


# Bitcoin Data Fetcher
def fetch_bitcoin_data(start_date='2020-01-01', end_date=None, period='max'):
    """
    Fetch Bitcoin data from Yahoo Finance
    
    Parameters:
    start_date (str): Start date in 'YYYY-MM-DD' format
    end_date (str): End date in 'YYYY-MM-DD' format (None for today)
    period (str): Period for data ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
    
    Returns:
    pandas.DataFrame: Bitcoin OHLCV data
    """
    try:
        import yfinance as yf
        print("Fetching Bitcoin data from Yahoo Finance...")
        
        # Bitcoin ticker symbol
        btc_ticker = "BTC-USD"
        
        # Create ticker object
        btc = yf.Ticker(btc_ticker)
        
        # Fetch historical data
        if start_date and end_date:
            btc_data = btc.history(start=start_date, end=end_date, interval='1d')
        elif start_date:
            btc_data = btc.history(start=start_date, interval='1d')
        else:
            btc_data = btc.history(period=period, interval='1d')
        
        if btc_data.empty:
            raise ValueError("No data retrieved. Check your date range or internet connection.")
        
        # Clean column names and select required columns
        btc_data.columns = btc_data.columns.str.lower()
        btc_data = btc_data[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # Remove timezone from datetime index (Excel compatibility)
        if btc_data.index.tz is not None:
            btc_data.index = btc_data.index.tz_localize(None)
        
        # Remove any rows with missing data
        btc_data = btc_data.dropna()
        
        print(f"✅ Successfully fetched {len(btc_data)} days of Bitcoin data")
        print(f"📅 Date range: {btc_data.index.min().date()} to {btc_data.index.max().date()}")
        print(f"💰 Price range: ${btc_data['close'].min():.2f} to ${btc_data['close'].max():.2f}")
        
        return btc_data
        
    except ImportError:
        print("❌ yfinance not installed. Installing now...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "yfinance"])
        
        # Try again after installation
        import yfinance as yf
        return fetch_bitcoin_data(start_date, end_date, period)
        
    except Exception as e:
        print(f"❌ Error fetching Bitcoin data: {e}")
        print("Using sample data instead...")
        return create_sample_data()

def create_sample_data():
    """Create sample Bitcoin data as fallback"""
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', '2024-01-01', freq='D')
    
    # Simulate realistic Bitcoin price data
    initial_price = 10000
    returns = np.random.normal(0.001, 0.04, len(dates))
    prices = [initial_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Create OHLCV data
    bitcoin_data = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.02))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.02))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(1000, 10000, len(dates))
    }, index=dates)
    
    # Ensure high >= close >= low and high >= open >= low
    bitcoin_data['high'] = bitcoin_data[['open', 'close', 'high']].max(axis=1)
    bitcoin_data['low'] = bitcoin_data[['open', 'close', 'low']].min(axis=1)
    
    return bitcoin_data

# Example usage and testing
def demo_usage(use_real_data=True, start_date='2020-01-01', end_date=None):
    """
    Demonstrate how to use the feature engine with real Bitcoin data
    
    Parameters:
    use_real_data (bool): Whether to fetch real data from Yahoo Finance
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    """
    
    if use_real_data:
        # Fetch real Bitcoin data from Yahoo Finance
        bitcoin_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    else:
        # Use sample data
        bitcoin_data = create_sample_data()
        print("Using sample Bitcoin data")
    
    print("\n📊 Bitcoin OHLCV Data Preview:")
    print(bitcoin_data.head())
    print(f"\n📈 Data shape: {bitcoin_data.shape}")
    
    # Basic statistics
    print(f"\n💹 Current Bitcoin Price: ${bitcoin_data['close'].iloc[-1]:,.2f}")
    print(f"📊 30-day return: {((bitcoin_data['close'].iloc[-1] / bitcoin_data['close'].iloc[-30]) - 1) * 100:.2f}%")
    print(f"📊 YTD return: {((bitcoin_data['close'].iloc[-1] / bitcoin_data['close'].iloc[0]) - 1) * 100:.2f}%")
    
    # Initialize feature engine
    print(f"\n🔧 Initializing Bitcoin Feature Engine...")
    feature_engine = BitcoinFeatureEngine(bitcoin_data)
    
    # Calculate all features
    featured_data = feature_engine.calculate_all_features()
    
    print(f"\n✅ Features calculated! New shape: {featured_data.shape}")
    print(f"📊 Total features created: {featured_data.shape[1] - 5}")  # Subtract OHLCV columns
    
    # Show key features
    print("\n" + "="*50)
    print("🎯 KEY FEATURES ANALYSIS")
    print("="*50)
    
    print("\n📈 Return Skewness (10-day) - Last 10 days:")
    skew_data = featured_data['return_skewness_10d'].tail(10)
    for date, value in skew_data.items():
        if not pd.isna(value):
            interpretation = "📉 Crash risk" if value < -0.5 else "📈 Upside bias" if value > 0.5 else "⚖️ Balanced"
            print(f"  {date.date()}: {value:.3f} {interpretation}")
    
    print("\n🔄 Return Persistence - Last 10 days:")
    persist_data = featured_data['return_persistence'].tail(10)
    for date, value in persist_data.items():
        if not pd.isna(value):
            interpretation = "🚀 Strong trend" if value > 0.8 else "📊 Choppy" if value < 0.6 else "📈 Moderate trend"
            print(f"  {date.date()}: {value:.3f} {interpretation}")
    
    # Current market conditions
    latest = featured_data.iloc[-1]
    print(f"\n🔍 CURRENT MARKET CONDITIONS:")
    print(f"💰 Price vs 20-day MA: {latest['price_vs_ma20']:.2f}%")
    print(f"📊 20-day Volatility: {latest['volatility_20d']:.1f}%")
    print(f"📈 10-day Momentum: {latest['price_momentum_10d']:.2f}%")
    print(f"🎯 30-day Price Percentile: {latest['price_percentile_30d']:.1f}%")
    
    # Get feature tiers
    print("\n" + "="*50)
    print("📊 TIER 1 FEATURES (Essential)")
    print("="*50)
    tier1 = feature_engine.get_tier1_features()
    print(tier1.tail(3))
    
    print("\n" + "="*50)
    print("📈 TIER 2 FEATURES (High Value)")
    print("="*50)
    tier2 = feature_engine.get_tier2_features()
    print(tier2.tail(3))
    
    print("\n" + "="*50)
    print("🔬 TIER 3 FEATURES (Advanced)")
    print("="*50)
    tier3 = feature_engine.get_tier3_features()
    print(tier3.tail(3))
    
    # Feature correlation analysis
    print("\n📊 Feature Correlation with Future Returns:")
    future_return = featured_data['log_return_1d'].shift(-1)  # Next day return
    
    key_features = ['return_skewness_10d', 'return_persistence', 'volatility_ratio', 
                   'price_vs_ma20', 'z_score_20d']
    
    correlations = {}
    for feature in key_features:
        if feature in featured_data.columns:
            corr = featured_data[feature].corr(future_return)
            correlations[feature] = corr
            print(f"  {feature}: {corr:.3f}")
    
    return featured_data, feature_engine

# Quick analysis function
def quick_bitcoin_analysis(days_back=30):
    """Quick Bitcoin analysis for recent data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.Timedelta(days=days_back + 200)  # Extra days for feature calculation
    
    btc_data = fetch_bitcoin_data(start_date=start_date.strftime('%Y-%m-%d'))
    engine = BitcoinFeatureEngine(btc_data)
    featured_data = engine.calculate_all_features()
    
    # Focus on recent data
    recent_data = featured_data.tail(days_back)
    
    print(f"\n🔍 BITCOIN ANALYSIS - Last {days_back} Days")
    print("="*50)
    
    latest = recent_data.iloc[-1]
    
    print(f"💰 Current Price: ${latest['close']:,.2f}")
    print(f"📊 Daily Return: {latest['log_return_1d']*100:.2f}%")
    print(f"📈 Return Skewness: {latest['return_skewness_10d']:.3f}")
    print(f"🔄 Return Persistence: {latest['return_persistence']:.3f}")
    print(f"⚡ Volatility Regime: {latest['volatility_regime']:.0f} (0=Low, 1=Med, 2=High)")
    print(f"🎯 Trend Strength: {latest['trend_strength']:.3f}")
    
    return recent_data

# Excel Export Functions - Single Sheet Version
def save_to_excel_single_sheet(featured_data, filename=None, include_analysis=True):
    """
    Save Bitcoin data with features to a single Excel worksheet
    
    Parameters:
    featured_data (pd.DataFrame): DataFrame with all features
    filename (str): Output filename (None for auto-generated name)
    include_analysis (bool): Whether to include analysis columns
    
    Returns:
    str: Path to saved file
    """
    try:
        if filename is None:
            # Auto-generate filename with current date
            current_date = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            filename = f'bitcoin_features_{current_date}.xlsx'
        
        print(f"💾 Saving Bitcoin data to single Excel sheet: {filename}")
        
        # Prepare the data
        export_data = featured_data.copy()
        
        # Remove timezone from index if present (Excel compatibility)
        if export_data.index.tz is not None:
            export_data.index = export_data.index.tz_localize(None)
        
        # Reset index to make date a column
        export_data = export_data.reset_index()
        export_data.rename(columns={'index': 'Date'}, inplace=True)
        
        if include_analysis:
            # Add interpretation columns for key features
            export_data = add_interpretation_columns(export_data)
        
        # Create Excel writer with formatting options
        with pd.ExcelWriter(filename, engine='openpyxl', 
                          options={'remove_timezone': True}) as writer:
            
            # Save to single sheet
            export_data.to_excel(writer, sheet_name='Bitcoin_Data_Features', 
                               index=False, startrow=0)
            
            # Get the worksheet for formatting
            worksheet = writer.sheets['Bitcoin_Data_Features']
            
            # Format the worksheet
            format_excel_worksheet(worksheet, export_data)
            
            print("✅ Bitcoin data with all features saved to single sheet")
        
        print(f"\n🎉 Successfully saved Bitcoin data to: {filename}")
        print(f"📊 Total rows: {len(export_data)}")
        print(f"📈 Total columns: {len(export_data.columns)}")
        print(f"📅 Date range: {export_data['Date'].min().date()} to {export_data['Date'].max().date()}")
        
        return filename
        
    except Exception as e:
        print(f"❌ Error saving to Excel: {e}")
        return None

def add_interpretation_columns(df):
    """Add interpretation columns for key features"""
    
    # Return Skewness Interpretation
    def interpret_skewness(value):
        if pd.isna(value):
            return ""
        elif value < -0.5:
            return "Crash Risk"
        elif value > 0.5:
            return "Upside Bias"
        else:
            return "Balanced"
    
    # Return Persistence Interpretation
    def interpret_persistence(value):
        if pd.isna(value):
            return ""
        elif value > 0.8:
            return "Strong Trend"
        elif value < 0.6:
            return "Choppy"
        else:
            return "Moderate Trend"
    
    # Volatility Regime Interpretation
    def interpret_vol_regime(value):
        if pd.isna(value):
            return ""
        elif value == 0:
            return "Low Vol"
        elif value == 1:
            return "Medium Vol"
        else:
            return "High Vol"
    
    # Price vs MA20 Interpretation
    def interpret_price_vs_ma(value):
        if pd.isna(value):
            return ""
        elif value > 5:
            return "Strong Uptrend"
        elif value > 0:
            return "Uptrend"
        elif value > -5:
            return "Downtrend"
        else:
            return "Strong Downtrend"
    
    # RSI Interpretation
    def interpret_rsi(value):
        if pd.isna(value):
            return ""
        elif value > 70:
            return "Overbought"
        elif value < 30:
            return "Oversold"
        else:
            return "Neutral"
    
    # Add interpretation columns after their respective feature columns
    
    # Find column positions
    col_positions = {}
    for i, col in enumerate(df.columns):
        col_positions[col] = i
    
    # Add interpretations
    if 'return_skewness_10d' in df.columns:
        pos = col_positions['return_skewness_10d'] + 1
        df.insert(pos, 'skewness_interpretation', 
                 df['return_skewness_10d'].apply(interpret_skewness))
    
    if 'return_persistence' in df.columns:
        pos = col_positions.get('skewness_interpretation', col_positions['return_persistence']) + 1
        df.insert(pos, 'persistence_interpretation', 
                 df['return_persistence'].apply(interpret_persistence))
    
    if 'volatility_regime' in df.columns:
        pos = col_positions.get('persistence_interpretation', col_positions['volatility_regime']) + 1
        df.insert(pos, 'vol_regime_interpretation', 
                 df['volatility_regime'].apply(interpret_vol_regime))
    
    if 'price_vs_ma20' in df.columns:
        pos = col_positions.get('vol_regime_interpretation', col_positions['price_vs_ma20']) + 1
        df.insert(pos, 'trend_interpretation', 
                 df['price_vs_ma20'].apply(interpret_price_vs_ma))
    
    if 'rsi' in df.columns:
        pos = col_positions.get('trend_interpretation', col_positions['rsi']) + 1
        df.insert(pos, 'rsi_interpretation', 
                 df['rsi'].apply(interpret_rsi))
    
    return df

def format_excel_worksheet(worksheet, df):
    """Format the Excel worksheet for better readability"""
    try:
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        from openpyxl.utils import get_column_letter
        
        # Header formatting
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        # Apply header formatting
        for col in range(1, len(df.columns) + 1):
            cell = worksheet.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center")
        
        # Date column formatting
        date_col = get_column_letter(1)  # Date is first column
        for row in range(2, len(df) + 2):
            cell = worksheet[f"{date_col}{row}"]
            cell.number_format = 'YYYY-MM-DD'
        
        # Price columns formatting (currency)
        price_cols = ['open', 'high', 'low', 'close']
        for col_name in price_cols:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = get_column_letter(col_idx)
                for row in range(2, len(df) + 2):
                    cell = worksheet[f"{col_letter}{row}"]
                    cell.number_format = '$#,##0.00'
        
        # Percentage columns formatting
        pct_cols = [col for col in df.columns if 'return' in col.lower() or 'vs_ma' in col or 'volatility' in col]
        for col_name in pct_cols:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = get_column_letter(col_idx)
                for row in range(2, len(df) + 2):
                    cell = worksheet[f"{col_letter}{row}"]
                    if 'volatility' in col_name:
                        cell.number_format = '0.00%'
                    else:
                        cell.number_format = '0.000'
        
        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 20)  # Max width of 20
            worksheet.column_dimensions[column_letter].width = adjusted_width
        
        # Freeze the header row
        worksheet.freeze_panes = "A2"
        
        print("✅ Excel formatting applied successfully")
        
    except ImportError:
        print("⚠️ openpyxl formatting not available - basic export completed")
    except Exception as e:
        print(f"⚠️ Excel formatting error: {e} - basic export completed")

# Updated download function for single sheet
def download_bitcoin_excel_single(start_date='2020-01-01', end_date=None, filename=None, include_analysis=True):
    """
    Complete workflow: Fetch Bitcoin data, calculate features, save to single Excel sheet
    
    Parameters:
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    filename (str): Output filename (None for auto-generated)
    include_analysis (bool): Whether to include interpretation columns
    
    Returns:
    tuple: (featured_data, filename)
    """
    print("🚀 Starting Bitcoin Data Download & Feature Engineering...")
    print("="*60)
    
    # Step 1: Fetch Bitcoin data
    print("📊 Step 1: Fetching Bitcoin data from Yahoo Finance...")
    btc_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    
    if btc_data is None or btc_data.empty:
        print("❌ Failed to fetch Bitcoin data")
        return None, None
    
    # Step 2: Calculate features
    print("\n🔧 Step 2: Calculating all price-based features...")
    try:
        feature_engine = BitcoinFeatureEngine(btc_data)
        featured_data = feature_engine.calculate_all_features()
        
        # Ensure no timezone issues
        if featured_data.index.tz is not None:
            featured_data.index = featured_data.index.tz_localize(None)
            
    except Exception as e:
        print(f"❌ Error calculating features: {e}")
        return None, None
    
    # Step 3: Save to single Excel sheet
    print(f"\n💾 Step 3: Saving to single Excel sheet...")
    saved_filename = save_to_excel_single_sheet(featured_data, filename=filename, include_analysis=include_analysis)
    
    if saved_filename:
        print(f"\n✅ COMPLETE! Bitcoin data saved to: {saved_filename}")
        
        # Show quick summary
        print(f"\n📋 QUICK SUMMARY:")
        print(f"📅 Data Period: {featured_data.index.min().date()} to {featured_data.index.max().date()}")
        print(f"📊 Total Records: {len(featured_data):,}")
        print(f"🔢 Total Features: {len(featured_data.columns)}")
        print(f"💰 Current Price: ${featured_data['close'].iloc[-1]:,.2f}")
        print(f"📈 30-day Return: {((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%")
        
        return featured_data, saved_filename
    else:
        return featured_data, None

# Updated quick download functions for single sheet
def download_bitcoin_1year_single(filename=None):
    """Download last 1 year of Bitcoin data to single sheet"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=1)
    return download_bitcoin_excel_single(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_1year_single.xlsx'
    )

def download_bitcoin_2years_single(filename=None):
    """Download last 2 years of Bitcoin data to single sheet"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=2)
    return download_bitcoin_excel_single(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_2years_single.xlsx'
    )

def download_bitcoin_5years_single(filename=None):
    """Download last 5 years of Bitcoin data to single sheet"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=5)
    return download_bitcoin_excel_single(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_5years_single.xlsx'
    )

def download_bitcoin_max_single(filename=None):
    """Download maximum available Bitcoin data to single sheet"""
    return download_bitcoin_excel_single(
        start_date='2010-01-01',
        filename=filename or 'bitcoin_max_data_single.xlsx'
    )

def create_excel_summary(featured_data):
    """Create summary statistics for Excel export"""
    latest = featured_data.iloc[-1]
    
    summary = {
        'Metric': [
            'Current Price',
            'Daily Return (%)',
            '30-day Return (%)',
            'YTD Return (%)',
            '20-day Volatility (%)',
            'Return Skewness (10d)',
            'Return Persistence',
            'Price vs MA20 (%)',
            'Price vs MA50 (%)',
            'Volatility Regime',
            'Trend Strength',
            'RSI',
            '30-day Price Percentile',
            'Z-Score (20d)'
        ],
        'Value': [
            f"${latest['close']:,.2f}",
            f"{latest['log_return_1d']*100:.2f}%",
            f"{((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%",
            f"{((featured_data['close'].iloc[-1] / featured_data['close'].iloc[0]) - 1) * 100:.2f}%",
            f"{latest['volatility_20d']:.1f}%",
            f"{latest['return_skewness_10d']:.3f}",
            f"{latest['return_persistence']:.3f}",
            f"{latest['price_vs_ma20']:.2f}%",
            f"{latest['price_vs_ma50']:.2f}%",
            f"{latest['volatility_regime']:.0f}",
            f"{latest['trend_strength']:.3f}",
            f"{latest['rsi']:.1f}",
            f"{latest['price_percentile_30d']:.1f}%",
            f"{latest['z_score_20d']:.3f}"
        ],
        'Interpretation': [
            'Current Bitcoin price',
            'Today\'s price change',
            'Monthly performance',
            'Year-to-date performance',
            'Risk/uncertainty level',
            'Asymmetry in returns',
            'Directional consistency',
            'Short-term trend position',
            'Medium-term trend position',
            '0=Low, 1=Med, 2=High vol',
            'Trend consistency (0-1)',
            'Momentum indicator (0-100)',
            'Relative price position',
            'Distance from mean'
        ]
    }
    
    return pd.DataFrame(summary)

def get_tier_features(featured_data, tier=1):
    """Get specific tier features for Excel export"""
    if tier == 1:
        features = ['log_return_1d', 'volatility_20d', 'price_vs_ma20', 'return_momentum_5d', 'daily_range']
    elif tier == 2:
        features = ['volatility_ratio', 'price_vs_ma50', 'ma20_vs_ma50', 'price_percentile_30d', 'return_acceleration']
    elif tier == 3:
        features = ['z_score_20d', 'trend_persistence', 'volatility_momentum', 'return_skewness_10d', 'return_persistence']
    else:
        return pd.DataFrame()
    
    available_features = [f for f in features if f in featured_data.columns]
    return featured_data[available_features]

def create_market_analysis(featured_data):
    """Create market analysis summary for Excel"""
    latest = featured_data.iloc[-1]
    
    # Market condition interpretations
    analysis = []
    
    # Price trend analysis
    if latest['price_vs_ma20'] > 5:
        trend_analysis = "Strong uptrend - Price well above 20-day MA"
    elif latest['price_vs_ma20'] > 0:
        trend_analysis = "Mild uptrend - Price above 20-day MA"
    elif latest['price_vs_ma20'] > -5:
        trend_analysis = "Mild downtrend - Price below 20-day MA"
    else:
        trend_analysis = "Strong downtrend - Price well below 20-day MA"
    
    # Volatility analysis
    if latest['volatility_regime'] == 2:
        vol_analysis = "High volatility environment - Expect large price swings"
    elif latest['volatility_regime'] == 1:
        vol_analysis = "Medium volatility - Normal market conditions"
    else:
        vol_analysis = "Low volatility - Calm market conditions"
    
    # Momentum analysis
    if latest['return_persistence'] > 0.8:
        momentum_analysis = "Strong momentum - Trend likely to continue"
    elif latest['return_persistence'] > 0.6:
        momentum_analysis = "Moderate momentum - Some directional bias"
    else:
        momentum_analysis = "Weak momentum - Choppy/sideways movement"
    
    # Risk analysis
    if latest['return_skewness_10d'] < -0.5:
        risk_analysis = "Elevated crash risk - Recent negative skew in returns"
    elif latest['return_skewness_10d'] > 0.5:
        risk_analysis = "Upside bias - Recent positive skew in returns"
    else:
        risk_analysis = "Balanced risk - Symmetric return distribution"
    
    analysis = [
        {'Category': 'Price Trend', 'Analysis': trend_analysis, 'Value': f"{latest['price_vs_ma20']:.2f}%"},
        {'Category': 'Volatility', 'Analysis': vol_analysis, 'Value': f"{latest['volatility_20d']:.1f}%"},
        {'Category': 'Momentum', 'Analysis': momentum_analysis, 'Value': f"{latest['return_persistence']:.3f}"},
        {'Category': 'Risk Profile', 'Analysis': risk_analysis, 'Value': f"{latest['return_skewness_10d']:.3f}"},
    ]
    
    return pd.DataFrame(analysis)

# Complete workflow function
def download_bitcoin_excel(start_date='2020-01-01', end_date=None, filename=None):
    """
    Complete workflow: Fetch Bitcoin data, calculate features, save to Excel
    
    Parameters:
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    filename (str): Output filename (None for auto-generated)
    
    Returns:
    tuple: (featured_data, filename)
    """
    print("🚀 Starting Bitcoin Data Download & Feature Engineering...")
    print("="*60)
    
    # Step 1: Fetch Bitcoin data
    print("📊 Step 1: Fetching Bitcoin data from Yahoo Finance...")
    btc_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    
    if btc_data is None or btc_data.empty:
        print("❌ Failed to fetch Bitcoin data")
        return None, None
    
    # Step 2: Calculate features
    print("\n🔧 Step 2: Calculating all price-based features...")
    try:
        feature_engine = BitcoinFeatureEngine(btc_data)
        featured_data = feature_engine.calculate_all_features()
        
        # Ensure no timezone issues
        if featured_data.index.tz is not None:
            featured_data.index = featured_data.index.tz_localize(None)
            
    except Exception as e:
        print(f"❌ Error calculating features: {e}")
        return None, None
    
    # Step 3: Save to Excel
    print(f"\n💾 Step 3: Saving to Excel...")
    saved_filename = save_to_excel(featured_data, filename=filename, include_summary=True)
    
    if saved_filename:
        print(f"\n✅ COMPLETE! Bitcoin data saved to: {saved_filename}")
        
        # Show quick summary
        print(f"\n📋 QUICK SUMMARY:")
        print(f"📅 Data Period: {featured_data.index.min().date()} to {featured_data.index.max().date()}")
        print(f"📊 Total Records: {len(featured_data):,}")
        print(f"🔢 Total Features: {len(featured_data.columns)}")
        print(f"💰 Current Price: ${featured_data['close'].iloc[-1]:,.2f}")
        print(f"📈 30-day Return: {((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%")
        
        return featured_data, saved_filename
    else:
        return featured_data, None

# Quick download functions for different periods
def download_bitcoin_1year(filename=None):
    """Download last 1 year of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=1)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_1year.xlsx'
    )

def download_bitcoin_2years(filename=None):
    """Download last 2 years of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=2)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_2years.xlsx'
    )

def download_bitcoin_5years(filename=None):
    """Download last 5 years of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=5)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_5years.xlsx'
    )

def download_bitcoin_max(filename=None):
    """Download maximum available Bitcoin data"""
    return download_bitcoin_excel(
        start_date='2010-01-01',
        filename=filename or 'bitcoin_max_data.xlsx'
    )

if __name__ == "__main__":
    # Example usage - Download Bitcoin data to single Excel sheet
    print("🚀 Bitcoin Feature Engineering & Single Sheet Excel Export")
    print("=" * 60)
    
    # Option 1: Download last 2 years to single sheet with analysis
    print("\n📊 Downloading last 2 years of Bitcoin data to single sheet...")
    data, filename = download_bitcoin_2years_single()
    
    # Option 2: Custom date range to single sheet
    # data, filename = download_bitcoin_excel_single(
    #     start_date='2023-01-01', 
    #     end_date='2024-12-31',
    #     filename='bitcoin_2023_2024_single.xlsx'
    # )
    
    # Option 3: Maximum data available to single sheet
    # data, filename = download_bitcoin_max_single()
    
    # Option 4: Without interpretation columns (just raw data + features)
    # data, filename = download_bitcoin_excel_single(
    #     start_date='2022-01-01',
    #     filename='bitcoin_raw_features.xlsx',
    #     include_analysis=False
    # )
    
    if filename:
        print(f"\n🎉 Success! Check your file: {filename}")
        print(f"\n📊 File contains:")
        print(f"  • Date column (YYYY-MM-DD format)")
        print(f"  • OHLCV price data")
        print(f"  • 45+ technical features")
        print(f"  • Interpretation columns for key features")
        print(f"  • Professional Excel formatting")
    else:
        print("\n❌ Download failed!")

import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class BitcoinFeatureEngine:
    """
    Comprehensive feature engineering for Bitcoin price prediction
    Calculates all price-based features from the documentation
    """
    
    def __init__(self, df):
        """
        Initialize with OHLCV dataframe
        Expected columns: ['open', 'high', 'low', 'close', 'volume']
        Index should be datetime
        """
        self.df = df.copy()
        self.df.columns = self.df.columns.str.lower()
        required_cols = ['open', 'high', 'low', 'close']
        
        if not all(col in self.df.columns for col in required_cols):
            raise ValueError(f"DataFrame must contain columns: {required_cols}")
    
    def calculate_all_features(self):
        """Calculate all price-based features"""
        print("Calculating Bitcoin price-based features...")
        
        # 1. Returns Features
        self._calculate_returns()
        
        # 2. Volatility Features  
        self._calculate_volatility()
        
        # 3. Moving Average Features
        self._calculate_ma_features()
        
        # 4. High-Low Range Features
        self._calculate_range_features()
        
        # 5. Price Position and Momentum
        self._calculate_position_momentum()
        
        # 6. Advanced Features
        self._calculate_advanced_features()
        
        return self.df
    
    def _calculate_returns(self):
        """Calculate return-based features"""
        print("  - Returns features...")
        
        # Basic returns
        self.df['simple_return_1d'] = self.df['close'].pct_change()
        self.df['log_return_1d'] = np.log(self.df['close'] / self.df['close'].shift(1))
        
        # Multi-period returns
        self.df['log_return_3d'] = self.df['log_return_1d'].rolling(3).sum()
        self.df['log_return_7d'] = self.df['log_return_1d'].rolling(7).sum()
        self.df['log_return_30d'] = self.df['log_return_1d'].rolling(30).sum()
        
        # Return derivatives
        self.df['return_momentum_5d'] = self.df['log_return_1d'].rolling(5).mean()
        self.df['return_acceleration'] = self.df['log_return_1d'].diff()
        
        # Return volatility ratio
        rolling_vol = self.df['log_return_1d'].rolling(20).std()
        self.df['return_volatility_ratio'] = self.df['log_return_1d'] / rolling_vol
        
        # Return skewness (KEY FEATURE)
        self.df['return_skewness_10d'] = self.df['log_return_1d'].rolling(10).apply(
            lambda x: stats.skew(x.dropna()) if len(x.dropna()) >= 3 else np.nan
        )
        
        # Return persistence (KEY FEATURE)
        self.df['return_persistence'] = self._calculate_return_persistence()
        
        # Risk-adjusted returns
        self.df['sharpe_10d'] = (self.df['return_momentum_5d'] * np.sqrt(252)) / (rolling_vol * np.sqrt(252))
        
        # Return autocorrelation
        self.df['return_autocorr_5d'] = self.df['log_return_1d'].rolling(10).apply(
            lambda x: x.autocorr(lag=1) if len(x.dropna()) > 5 else np.nan
        )
    
    def _calculate_return_persistence(self):
        """Calculate return persistence - consistency of return direction"""
        returns = self.df['log_return_1d']
        
        # Method 1: Directional consistency ratio (5-day window)
        return_signs = np.sign(returns)
        consistency = return_signs.rolling(5).apply(
            lambda x: len(x[x == x.iloc[-1]]) / len(x) if len(x) > 0 else np.nan
        )
        
        return consistency
    
    def _calculate_volatility(self):
        """Calculate volatility-based features"""
        print("  - Volatility features...")
        
        returns = self.df['log_return_1d']
        
        # Historical volatility (different windows)
        self.df['volatility_5d'] = returns.rolling(5).std() * np.sqrt(252)
        self.df['volatility_20d'] = returns.rolling(20).std() * np.sqrt(252)
        self.df['volatility_60d'] = returns.rolling(60).std() * np.sqrt(252)
        
        # Parkinson volatility (high-low based)
        try:
            self.df['parkinson_vol'] = np.sqrt(
                np.log(self.df['high'] / self.df['low']).rolling(20).apply(
                    lambda x: (x**2).mean() / (4 * np.log(2)) if len(x) > 0 else np.nan
                )
            ) * np.sqrt(252)
        except:
            self.df['parkinson_vol'] = self.df['volatility_20d']  # Fallback
        
        # Garman-Klass volatility
        try:
            self.df['garman_klass_vol'] = self._calculate_garman_klass()
        except:
            self.df['garman_klass_vol'] = self.df['volatility_20d']  # Fallback
        
        # Volatility relationships
        self.df['volatility_ratio'] = self.df['volatility_5d'] / self.df['volatility_60d']
        self.df['volatility_momentum'] = self.df['volatility_20d'].pct_change(5)
        
        # Volatility regime classification (fixed)
        vol_20d = self.df['volatility_20d']
        try:
            vol_low = vol_20d.rolling(252).quantile(0.33)
            vol_high = vol_20d.rolling(252).quantile(0.67)
            
            # Create volatility regime: 0=Low, 1=Medium, 2=High
            conditions = [
                vol_20d <= vol_low,
                (vol_20d > vol_low) & (vol_20d <= vol_high),
                vol_20d > vol_high
            ]
            self.df['volatility_regime'] = np.select(conditions, [0, 1, 2], default=np.nan)
        except:
            # Simple fallback regime classification
            vol_median = vol_20d.rolling(252).median()
            self.df['volatility_regime'] = np.where(vol_20d > vol_median, 1, 0)
        
        # Volatility mean reversion
        vol_mean = self.df['volatility_20d'].rolling(252).mean()
        self.df['volatility_mean_reversion'] = (self.df['volatility_20d'] - vol_mean) / vol_mean
        
        # Advanced volatility features
        try:
            self.df['volatility_clustering'] = self._calculate_vol_clustering()
        except:
            self.df['volatility_clustering'] = self.df['volatility_20d'].rolling(10).std()
            
        self.df['vol_of_vol'] = self.df['volatility_20d'].rolling(20).std()
    
    def _calculate_garman_klass(self):
        """Calculate Garman-Klass volatility estimator"""
        o, h, l, c = self.df['open'], self.df['high'], self.df['low'], self.df['close']
        
        term1 = 0.5 * (np.log(h/l))**2
        term2 = (2*np.log(2) - 1) * (np.log(c/o))**2
        
        gk_vol = (term1 - term2).rolling(20).mean()
        return np.sqrt(gk_vol * 252)
    
    def _calculate_vol_clustering(self):
        """Measure volatility clustering/persistence"""
        vol_changes = self.df['volatility_5d'].pct_change().abs()
        return vol_changes.rolling(10).std()
    
    def _calculate_ma_features(self):
        """Calculate moving average features"""
        print("  - Moving average features...")
        
        close = self.df['close']
        
        # Moving averages
        self.df['ma20'] = close.rolling(20).mean()
        self.df['ma50'] = close.rolling(50).mean()
        self.df['ma200'] = close.rolling(200).mean()
        
        # Distance from MAs (percentage)
        self.df['price_vs_ma20'] = (close - self.df['ma20']) / self.df['ma20'] * 100
        self.df['price_vs_ma50'] = (close - self.df['ma50']) / self.df['ma50'] * 100
        self.df['price_vs_ma200'] = (close - self.df['ma200']) / self.df['ma200'] * 100
        
        # MA relationships
        self.df['ma20_vs_ma50'] = (self.df['ma20'] - self.df['ma50']) / self.df['ma50'] * 100
        self.df['ma50_vs_ma200'] = (self.df['ma50'] - self.df['ma200']) / self.df['ma200'] * 100
        
        # MA convergence
        ma_spread = np.abs(self.df['ma20'] - self.df['ma50']) + np.abs(self.df['ma50'] - self.df['ma200'])
        self.df['ma_convergence'] = ma_spread / close
        
        # MA slopes (momentum)
        self.df['ma20_slope'] = self.df['ma20'].pct_change(5) * 100
        self.df['ma50_slope'] = self.df['ma50'].pct_change(5) * 100
        self.df['ma_acceleration'] = self.df['ma20_slope'].diff()
        
        # Binary position features
        self.df['price_above_ma20'] = (close > self.df['ma20']).astype(int)
        self.df['price_above_ma50'] = (close > self.df['ma50']).astype(int)
        self.df['price_above_all_mas'] = (
            (close > self.df['ma20']) & 
            (close > self.df['ma50']) & 
            (close > self.df['ma200'])
        ).astype(int)
        
        # Bullish alignment
        self.df['ma_bullish_alignment'] = (
            (self.df['ma20'] > self.df['ma50']) & 
            (self.df['ma50'] > self.df['ma200'])
        ).astype(int)
    
    def _calculate_range_features(self):
        """Calculate high-low range features"""
        print("  - Range features...")
        
        o, h, l, c = self.df['open'], self.df['high'], self.df['low'], self.df['close']
        prev_close = c.shift(1)
        
        # Daily range indicators
        self.df['daily_range'] = (h - l) / c * 100
        avg_range = self.df['daily_range'].rolling(20).mean()
        self.df['range_vs_average'] = self.df['daily_range'] / avg_range
        self.df['range_position'] = (c - l) / (h - l)
        self.df['gap_open'] = (o - prev_close) / prev_close * 100
        
        # True Range and ATR
        tr1 = h - l
        tr2 = np.abs(h - prev_close)
        tr3 = np.abs(l - prev_close)
        self.df['true_range'] = np.maximum(tr1, np.maximum(tr2, tr3))
        self.df['average_true_range'] = self.df['true_range'].rolling(14).mean()
        
        # Range expansion/contraction
        range_ma = self.df['daily_range'].rolling(10).mean()
        self.df['range_expansion'] = self.df['daily_range'] / range_ma
        self.df['range_contraction'] = 1 / self.df['range_expansion']
    
    def _calculate_position_momentum(self):
        """Calculate price position and momentum features"""
        print("  - Position and momentum features...")
        
        close = self.df['close']
        
        # Price percentiles
        self.df['price_percentile_30d'] = close.rolling(30).rank(pct=True) * 100
        self.df['price_percentile_90d'] = close.rolling(90).rank(pct=True) * 100
        
        # Distance from highs/lows
        rolling_high_30d = close.rolling(30).max()
        rolling_low_30d = close.rolling(30).min()
        self.df['distance_from_high'] = (rolling_high_30d - close) / close * 100
        self.df['distance_from_low'] = (close - rolling_low_30d) / close * 100
        
        # Momentum features
        self.df['price_momentum_10d'] = close.pct_change(10) * 100
        
        # RSI-style momentum
        delta = close.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(14).mean()
        avg_loss = loss.rolling(14).mean()
        rs = avg_gain / avg_loss
        self.df['rsi'] = 100 - (100 / (1 + rs))
        
        # Momentum persistence
        momentum_sign = np.sign(self.df['price_momentum_10d'])
        self.df['momentum_persistence'] = momentum_sign.rolling(5).apply(
            lambda x: (x == x.iloc[-1]).sum() / len(x) if len(x) > 0 else np.nan
        )
    
    def _calculate_advanced_features(self):
        """Calculate advanced price-based features"""
        print("  - Advanced features...")
        
        close = self.df['close']
        returns = self.df['log_return_1d']
        
        # Z-scores (mean reversion indicators)
        rolling_mean_20d = close.rolling(20).mean()
        rolling_std_20d = close.rolling(20).std()
        self.df['z_score_20d'] = (close - rolling_mean_20d) / rolling_std_20d
        
        rolling_mean_60d = close.rolling(60).mean()
        rolling_std_60d = close.rolling(60).std()
        self.df['z_score_60d'] = (close - rolling_mean_60d) / rolling_std_60d
        
        # Bollinger Bands position
        bb_upper = rolling_mean_20d + (2 * rolling_std_20d)
        bb_lower = rolling_mean_20d - (2 * rolling_std_20d)
        self.df['bollinger_position'] = (close - bb_lower) / (bb_upper - bb_lower)
        
        # Mean reversion pressure
        long_term_mean = close.rolling(252).mean()
        self.df['mean_reversion_pressure'] = (close - long_term_mean) / long_term_mean * 100
        
        # Trend quality features
        self.df['trend_strength'] = self._calculate_trend_strength()
        self.df['trend_persistence'] = self._calculate_trend_persistence()
        
        # Market microstructure approximations
        self.df['noise_ratio'] = self._calculate_noise_ratio()
        
        # Hurst exponent (trend vs mean reversion tendency)
        self.df['hurst_exponent'] = returns.rolling(50).apply(
            lambda x: self._calculate_hurst(x.dropna()) if len(x.dropna()) > 10 else np.nan
        )
    
    def _calculate_trend_strength(self):
        """Calculate trend strength based on price consistency"""
        close = self.df['close']
        trend_up = (close > close.shift(1)).rolling(10).sum() / 10
        trend_down = (close < close.shift(1)).rolling(10).sum() / 10
        return np.maximum(trend_up, trend_down)
    
    def _calculate_trend_persistence(self):
        """Calculate how long trends persist"""
        price_changes = np.sign(self.df['close'].diff())
        
        # Calculate run lengths
        runs = []
        current_run = 1
        
        for i in range(1, len(price_changes)):
            if price_changes.iloc[i] == price_changes.iloc[i-1]:
                current_run += 1
            else:
                runs.append(current_run)
                current_run = 1
        
        # Rolling average of recent run lengths
        trend_persistence = pd.Series(index=self.df.index, dtype=float)
        window = 20
        
        for i in range(window, len(self.df)):
            recent_runs = [r for r in runs if r > 0][-window:]
            if recent_runs:
                trend_persistence.iloc[i] = np.mean(recent_runs)
        
        return trend_persistence
    
    def _calculate_noise_ratio(self):
        """Estimate noise vs signal in price movements"""
        returns = self.df['log_return_1d']
        
        # Compare actual volatility to theoretical random walk
        actual_vol = returns.rolling(20).std()
        
        # Theoretical vol if purely random
        daily_returns = returns.rolling(20).apply(lambda x: np.mean(np.abs(x)))
        theoretical_vol = daily_returns * np.sqrt(np.pi/2)  # For random walk
        
        return actual_vol / theoretical_vol
    
    def _calculate_hurst(self, returns):
        """Calculate Hurst exponent for trend vs mean reversion"""
        if len(returns) < 10:
            return np.nan
            
        try:
            # Simple R/S calculation
            n = len(returns)
            mean_return = np.mean(returns)
            
            # Cumulative deviations
            cum_devs = np.cumsum(returns - mean_return)
            
            # Range
            R = np.max(cum_devs) - np.min(cum_devs)
            
            # Standard deviation
            S = np.std(returns)
            
            if S == 0:
                return 0.5
                
            # R/S ratio
            rs_ratio = R / S
            
            # Hurst exponent approximation
            if rs_ratio > 0:
                return np.log(rs_ratio) / np.log(n)
            else:
                return 0.5
                
        except:
            return np.nan
    
    def get_tier1_features(self):
        """Return Tier 1 (essential) features"""
        tier1_features = [
            'log_return_1d',
            'volatility_20d', 
            'price_vs_ma20',
            'return_momentum_5d',
            'daily_range'
        ]
        
        available_features = [f for f in tier1_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_tier2_features(self):
        """Return Tier 2 (high value) features"""
        tier2_features = [
            'volatility_ratio',
            'price_vs_ma50',
            'ma20_vs_ma50', 
            'price_percentile_30d',
            'return_acceleration'
        ]
        
        available_features = [f for f in tier2_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_tier3_features(self):
        """Return Tier 3 (advanced) features"""
        tier3_features = [
            'z_score_20d',
            'trend_persistence',
            'volatility_momentum',
            'return_skewness_10d',
            'return_persistence'
        ]
        
        available_features = [f for f in tier3_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_feature_summary(self):
        """Get summary of all calculated features"""
        feature_cols = [col for col in self.df.columns 
                       if col not in ['open', 'high', 'low', 'close', 'volume']]
        
        summary = {}
        for col in feature_cols:
            summary[col] = {
                'count': self.df[col].count(),
                'mean': self.df[col].mean(),
                'std': self.df[col].std(),
                'min': self.df[col].min(),
                'max': self.df[col].max()
            }
        
        return pd.DataFrame(summary).T


# Bitcoin Data Fetcher
def fetch_bitcoin_data(start_date='2020-01-01', end_date=None, period='max'):
    """
    Fetch Bitcoin data from Yahoo Finance
    
    Parameters:
    start_date (str): Start date in 'YYYY-MM-DD' format
    end_date (str): End date in 'YYYY-MM-DD' format (None for today)
    period (str): Period for data ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
    
    Returns:
    pandas.DataFrame: Bitcoin OHLCV data
    """
    try:
        import yfinance as yf
        print("Fetching Bitcoin data from Yahoo Finance...")
        
        # Bitcoin ticker symbol
        btc_ticker = "BTC-USD"
        
        # Create ticker object
        btc = yf.Ticker(btc_ticker)
        
        # Fetch historical data
        if start_date and end_date:
            btc_data = btc.history(start=start_date, end=end_date, interval='1d')
        elif start_date:
            btc_data = btc.history(start=start_date, interval='1d')
        else:
            btc_data = btc.history(period=period, interval='1d')
        
        if btc_data.empty:
            raise ValueError("No data retrieved. Check your date range or internet connection.")
        
        # Clean column names and select required columns
        btc_data.columns = btc_data.columns.str.lower()
        btc_data = btc_data[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # Remove timezone from datetime index (Excel compatibility)
        if btc_data.index.tz is not None:
            btc_data.index = btc_data.index.tz_localize(None)
        
        # Remove any rows with missing data
        btc_data = btc_data.dropna()
        
        print(f"✅ Successfully fetched {len(btc_data)} days of Bitcoin data")
        print(f"📅 Date range: {btc_data.index.min().date()} to {btc_data.index.max().date()}")
        print(f"💰 Price range: ${btc_data['close'].min():.2f} to ${btc_data['close'].max():.2f}")
        
        return btc_data
        
    except ImportError:
        print("❌ yfinance not installed. Installing now...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "yfinance"])
        
        # Try again after installation
        import yfinance as yf
        return fetch_bitcoin_data(start_date, end_date, period)
        
    except Exception as e:
        print(f"❌ Error fetching Bitcoin data: {e}")
        print("Using sample data instead...")
        return create_sample_data()

def create_sample_data():
    """Create sample Bitcoin data as fallback"""
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', '2024-01-01', freq='D')
    
    # Simulate realistic Bitcoin price data
    initial_price = 10000
    returns = np.random.normal(0.001, 0.04, len(dates))
    prices = [initial_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Create OHLCV data
    bitcoin_data = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.02))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.02))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(1000, 10000, len(dates))
    }, index=dates)
    
    # Ensure high >= close >= low and high >= open >= low
    bitcoin_data['high'] = bitcoin_data[['open', 'close', 'high']].max(axis=1)
    bitcoin_data['low'] = bitcoin_data[['open', 'close', 'low']].min(axis=1)
    
    return bitcoin_data

# Example usage and testing
def demo_usage(use_real_data=True, start_date='2020-01-01', end_date=None):
    """
    Demonstrate how to use the feature engine with real Bitcoin data
    
    Parameters:
    use_real_data (bool): Whether to fetch real data from Yahoo Finance
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    """
    
    if use_real_data:
        # Fetch real Bitcoin data from Yahoo Finance
        bitcoin_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    else:
        # Use sample data
        bitcoin_data = create_sample_data()
        print("Using sample Bitcoin data")
    
    print("\n📊 Bitcoin OHLCV Data Preview:")
    print(bitcoin_data.head())
    print(f"\n📈 Data shape: {bitcoin_data.shape}")
    
    # Basic statistics
    print(f"\n💹 Current Bitcoin Price: ${bitcoin_data['close'].iloc[-1]:,.2f}")
    print(f"📊 30-day return: {((bitcoin_data['close'].iloc[-1] / bitcoin_data['close'].iloc[-30]) - 1) * 100:.2f}%")
    print(f"📊 YTD return: {((bitcoin_data['close'].iloc[-1] / bitcoin_data['close'].iloc[0]) - 1) * 100:.2f}%")
    
    # Initialize feature engine
    print(f"\n🔧 Initializing Bitcoin Feature Engine...")
    feature_engine = BitcoinFeatureEngine(bitcoin_data)
    
    # Calculate all features
    featured_data = feature_engine.calculate_all_features()
    
    print(f"\n✅ Features calculated! New shape: {featured_data.shape}")
    print(f"📊 Total features created: {featured_data.shape[1] - 5}")  # Subtract OHLCV columns
    
    # Show key features
    print("\n" + "="*50)
    print("🎯 KEY FEATURES ANALYSIS")
    print("="*50)
    
    print("\n📈 Return Skewness (10-day) - Last 10 days:")
    skew_data = featured_data['return_skewness_10d'].tail(10)
    for date, value in skew_data.items():
        if not pd.isna(value):
            interpretation = "📉 Crash risk" if value < -0.5 else "📈 Upside bias" if value > 0.5 else "⚖️ Balanced"
            print(f"  {date.date()}: {value:.3f} {interpretation}")
    
    print("\n🔄 Return Persistence - Last 10 days:")
    persist_data = featured_data['return_persistence'].tail(10)
    for date, value in persist_data.items():
        if not pd.isna(value):
            interpretation = "🚀 Strong trend" if value > 0.8 else "📊 Choppy" if value < 0.6 else "📈 Moderate trend"
            print(f"  {date.date()}: {value:.3f} {interpretation}")
    
    # Current market conditions
    latest = featured_data.iloc[-1]
    print(f"\n🔍 CURRENT MARKET CONDITIONS:")
    print(f"💰 Price vs 20-day MA: {latest['price_vs_ma20']:.2f}%")
    print(f"📊 20-day Volatility: {latest['volatility_20d']:.1f}%")
    print(f"📈 10-day Momentum: {latest['price_momentum_10d']:.2f}%")
    print(f"🎯 30-day Price Percentile: {latest['price_percentile_30d']:.1f}%")
    
    # Get feature tiers
    print("\n" + "="*50)
    print("📊 TIER 1 FEATURES (Essential)")
    print("="*50)
    tier1 = feature_engine.get_tier1_features()
    print(tier1.tail(3))
    
    print("\n" + "="*50)
    print("📈 TIER 2 FEATURES (High Value)")
    print("="*50)
    tier2 = feature_engine.get_tier2_features()
    print(tier2.tail(3))
    
    print("\n" + "="*50)
    print("🔬 TIER 3 FEATURES (Advanced)")
    print("="*50)
    tier3 = feature_engine.get_tier3_features()
    print(tier3.tail(3))
    
    # Feature correlation analysis
    print("\n📊 Feature Correlation with Future Returns:")
    future_return = featured_data['log_return_1d'].shift(-1)  # Next day return
    
    key_features = ['return_skewness_10d', 'return_persistence', 'volatility_ratio', 
                   'price_vs_ma20', 'z_score_20d']
    
    correlations = {}
    for feature in key_features:
        if feature in featured_data.columns:
            corr = featured_data[feature].corr(future_return)
            correlations[feature] = corr
            print(f"  {feature}: {corr:.3f}")
    
    return featured_data, feature_engine

# Quick analysis function
def quick_bitcoin_analysis(days_back=30):
    """Quick Bitcoin analysis for recent data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.Timedelta(days=days_back + 200)  # Extra days for feature calculation
    
    btc_data = fetch_bitcoin_data(start_date=start_date.strftime('%Y-%m-%d'))
    engine = BitcoinFeatureEngine(btc_data)
    featured_data = engine.calculate_all_features()
    
    # Focus on recent data
    recent_data = featured_data.tail(days_back)
    
    print(f"\n🔍 BITCOIN ANALYSIS - Last {days_back} Days")
    print("="*50)
    
    latest = recent_data.iloc[-1]
    
    print(f"💰 Current Price: ${latest['close']:,.2f}")
    print(f"📊 Daily Return: {latest['log_return_1d']*100:.2f}%")
    print(f"📈 Return Skewness: {latest['return_skewness_10d']:.3f}")
    print(f"🔄 Return Persistence: {latest['return_persistence']:.3f}")
    print(f"⚡ Volatility Regime: {latest['volatility_regime']:.0f} (0=Low, 1=Med, 2=High)")
    print(f"🎯 Trend Strength: {latest['trend_strength']:.3f}")
    
    return recent_data

# Excel Export Functions - Single Sheet Version
def save_to_excel_single_sheet(featured_data, filename=None, include_analysis=True):
    """
    Save Bitcoin data with features to a single Excel worksheet
    
    Parameters:
    featured_data (pd.DataFrame): DataFrame with all features
    filename (str): Output filename (None for auto-generated name)
    include_analysis (bool): Whether to include analysis columns
    
    Returns:
    str: Path to saved file
    """
    try:
        if filename is None:
            # Auto-generate filename with current date
            current_date = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            filename = f'bitcoin_features_{current_date}.xlsx'
        
        print(f"💾 Saving Bitcoin data to single Excel sheet: {filename}")
        
        # Prepare the data
        export_data = featured_data.copy()
        
        # Remove timezone from index if present (Excel compatibility)
        if export_data.index.tz is not None:
            export_data.index = export_data.index.tz_localize(None)
        
        # Reset index to make date a column
        export_data = export_data.reset_index()
        export_data.rename(columns={'index': 'Date'}, inplace=True)
        
        if include_analysis:
            # Add interpretation columns for key features
            export_data = add_interpretation_columns(export_data)
        
        # Create Excel writer with formatting options
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            
            # Save to single sheet
            export_data.to_excel(writer, sheet_name='Bitcoin_Data_Features', 
                               index=False, startrow=0)
            
            # Get the worksheet for formatting
            worksheet = writer.sheets['Bitcoin_Data_Features']
            
            # Format the worksheet
            format_excel_worksheet(worksheet, export_data)
            
            print("✅ Bitcoin data with all features saved to single sheet")
        
        print(f"\n🎉 Successfully saved Bitcoin data to: {filename}")
        print(f"📊 Total rows: {len(export_data)}")
        print(f"📈 Total columns: {len(export_data.columns)}")
        print(f"📅 Date range: {export_data['Date'].min().date()} to {export_data['Date'].max().date()}")
        
        return filename
        
    except Exception as e:
        print(f"❌ Error saving to Excel: {e}")
        return None

def add_interpretation_columns(df):
    """Add interpretation columns for key features"""
    
    # Return Skewness Interpretation
    def interpret_skewness(value):
        if pd.isna(value):
            return ""
        elif value < -0.5:
            return "Crash Risk"
        elif value > 0.5:
            return "Upside Bias"
        else:
            return "Balanced"
    
    # Return Persistence Interpretation
    def interpret_persistence(value):
        if pd.isna(value):
            return ""
        elif value > 0.8:
            return "Strong Trend"
        elif value < 0.6:
            return "Choppy"
        else:
            return "Moderate Trend"
    
    # Volatility Regime Interpretation
    def interpret_vol_regime(value):
        if pd.isna(value):
            return ""
        elif value == 0:
            return "Low Vol"
        elif value == 1:
            return "Medium Vol"
        else:
            return "High Vol"
    
    # Price vs MA20 Interpretation
    def interpret_price_vs_ma(value):
        if pd.isna(value):
            return ""
        elif value > 5:
            return "Strong Uptrend"
        elif value > 0:
            return "Uptrend"
        elif value > -5:
            return "Downtrend"
        else:
            return "Strong Downtrend"
    
    # RSI Interpretation
    def interpret_rsi(value):
        if pd.isna(value):
            return ""
        elif value > 70:
            return "Overbought"
        elif value < 30:
            return "Oversold"
        else:
            return "Neutral"
    
    # Add interpretation columns after their respective feature columns
    
    # Find column positions
    col_positions = {}
    for i, col in enumerate(df.columns):
        col_positions[col] = i
    
    # Add interpretations
    if 'return_skewness_10d' in df.columns:
        pos = col_positions['return_skewness_10d'] + 1
        df.insert(pos, 'skewness_interpretation', 
                 df['return_skewness_10d'].apply(interpret_skewness))
    
    if 'return_persistence' in df.columns:
        pos = col_positions.get('skewness_interpretation', col_positions['return_persistence']) + 1
        df.insert(pos, 'persistence_interpretation', 
                 df['return_persistence'].apply(interpret_persistence))
    
    if 'volatility_regime' in df.columns:
        pos = col_positions.get('persistence_interpretation', col_positions['volatility_regime']) + 1
        df.insert(pos, 'vol_regime_interpretation', 
                 df['volatility_regime'].apply(interpret_vol_regime))
    
    if 'price_vs_ma20' in df.columns:
        pos = col_positions.get('vol_regime_interpretation', col_positions['price_vs_ma20']) + 1
        df.insert(pos, 'trend_interpretation', 
                 df['price_vs_ma20'].apply(interpret_price_vs_ma))
    
    if 'rsi' in df.columns:
        pos = col_positions.get('trend_interpretation', col_positions['rsi']) + 1
        df.insert(pos, 'rsi_interpretation', 
                 df['rsi'].apply(interpret_rsi))
    
    return df

def format_excel_worksheet(worksheet, df):
    """Format the Excel worksheet for better readability"""
    try:
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        from openpyxl.utils import get_column_letter
        
        # Header formatting
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        # Apply header formatting
        for col in range(1, len(df.columns) + 1):
            cell = worksheet.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center")
        
        # Date column formatting
        date_col = get_column_letter(1)  # Date is first column
        for row in range(2, len(df) + 2):
            cell = worksheet[f"{date_col}{row}"]
            cell.number_format = 'YYYY-MM-DD'
        
        # Price columns formatting (currency)
        price_cols = ['open', 'high', 'low', 'close']
        for col_name in price_cols:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = get_column_letter(col_idx)
                for row in range(2, len(df) + 2):
                    cell = worksheet[f"{col_letter}{row}"]
                    cell.number_format = '$#,##0.00'
        
        # Percentage columns formatting
        pct_cols = [col for col in df.columns if 'return' in col.lower() or 'vs_ma' in col or 'volatility' in col]
        for col_name in pct_cols:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = get_column_letter(col_idx)
                for row in range(2, len(df) + 2):
                    cell = worksheet[f"{col_letter}{row}"]
                    if 'volatility' in col_name:
                        cell.number_format = '0.00%'
                    else:
                        cell.number_format = '0.000'
        
        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 20)  # Max width of 20
            worksheet.column_dimensions[column_letter].width = adjusted_width
        
        # Freeze the header row
        worksheet.freeze_panes = "A2"
        
        print("✅ Excel formatting applied successfully")
        
    except ImportError:
        print("⚠️ openpyxl formatting not available - basic export completed")
    except Exception as e:
        print(f"⚠️ Excel formatting error: {e} - basic export completed")

# Updated download function for single sheet
def download_bitcoin_excel_single(start_date='2020-01-01', end_date=None, filename=None, include_analysis=True):
    """
    Complete workflow: Fetch Bitcoin data, calculate features, save to single Excel sheet
    
    Parameters:
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    filename (str): Output filename (None for auto-generated)
    include_analysis (bool): Whether to include interpretation columns
    
    Returns:
    tuple: (featured_data, filename)
    """
    print("🚀 Starting Bitcoin Data Download & Feature Engineering...")
    print("="*60)
    
    # Step 1: Fetch Bitcoin data
    print("📊 Step 1: Fetching Bitcoin data from Yahoo Finance...")
    btc_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    
    if btc_data is None or btc_data.empty:
        print("❌ Failed to fetch Bitcoin data")
        return None, None
    
    # Step 2: Calculate features
    print("\n🔧 Step 2: Calculating all price-based features...")
    try:
        feature_engine = BitcoinFeatureEngine(btc_data)
        featured_data = feature_engine.calculate_all_features()
        
        # Ensure no timezone issues
        if featured_data.index.tz is not None:
            featured_data.index = featured_data.index.tz_localize(None)
            
    except Exception as e:
        print(f"❌ Error calculating features: {e}")
        return None, None
    
    # Step 3: Save to single Excel sheet
    print(f"\n💾 Step 3: Saving to single Excel sheet...")
    saved_filename = save_to_excel_single_sheet(featured_data, filename=filename, include_analysis=include_analysis)
    
    if saved_filename:
        print(f"\n✅ COMPLETE! Bitcoin data saved to: {saved_filename}")
        
        # Show quick summary
        print(f"\n📋 QUICK SUMMARY:")
        print(f"📅 Data Period: {featured_data.index.min().date()} to {featured_data.index.max().date()}")
        print(f"📊 Total Records: {len(featured_data):,}")
        print(f"🔢 Total Features: {len(featured_data.columns)}")
        print(f"💰 Current Price: ${featured_data['close'].iloc[-1]:,.2f}")
        print(f"📈 30-day Return: {((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%")
        
        return featured_data, saved_filename
    else:
        return featured_data, None

# Updated quick download functions for single sheet
def download_bitcoin_1year_single(filename=None):
    """Download last 1 year of Bitcoin data to single sheet"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=1)
    return download_bitcoin_excel_single(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_1year_single.xlsx'
    )

def download_bitcoin_2years_single(filename=None):
    """Download last 2 years of Bitcoin data to single sheet"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=2)
    return download_bitcoin_excel_single(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_2years_single.xlsx'
    )

def download_bitcoin_5years_single(filename=None):
    """Download last 5 years of Bitcoin data to single sheet"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=5)
    return download_bitcoin_excel_single(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_5years_single.xlsx'
    )

def download_bitcoin_max_single(filename=None):
    """Download maximum available Bitcoin data to single sheet"""
    return download_bitcoin_excel_single(
        start_date='2010-01-01',
        filename=filename or 'bitcoin_max_data_single.xlsx'
    )

def create_excel_summary(featured_data):
    """Create summary statistics for Excel export"""
    latest = featured_data.iloc[-1]
    
    summary = {
        'Metric': [
            'Current Price',
            'Daily Return (%)',
            '30-day Return (%)',
            'YTD Return (%)',
            '20-day Volatility (%)',
            'Return Skewness (10d)',
            'Return Persistence',
            'Price vs MA20 (%)',
            'Price vs MA50 (%)',
            'Volatility Regime',
            'Trend Strength',
            'RSI',
            '30-day Price Percentile',
            'Z-Score (20d)'
        ],
        'Value': [
            f"${latest['close']:,.2f}",
            f"{latest['log_return_1d']*100:.2f}%",
            f"{((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%",
            f"{((featured_data['close'].iloc[-1] / featured_data['close'].iloc[0]) - 1) * 100:.2f}%",
            f"{latest['volatility_20d']:.1f}%",
            f"{latest['return_skewness_10d']:.3f}",
            f"{latest['return_persistence']:.3f}",
            f"{latest['price_vs_ma20']:.2f}%",
            f"{latest['price_vs_ma50']:.2f}%",
            f"{latest['volatility_regime']:.0f}",
            f"{latest['trend_strength']:.3f}",
            f"{latest['rsi']:.1f}",
            f"{latest['price_percentile_30d']:.1f}%",
            f"{latest['z_score_20d']:.3f}"
        ],
        'Interpretation': [
            'Current Bitcoin price',
            'Today\'s price change',
            'Monthly performance',
            'Year-to-date performance',
            'Risk/uncertainty level',
            'Asymmetry in returns',
            'Directional consistency',
            'Short-term trend position',
            'Medium-term trend position',
            '0=Low, 1=Med, 2=High vol',
            'Trend consistency (0-1)',
            'Momentum indicator (0-100)',
            'Relative price position',
            'Distance from mean'
        ]
    }
    
    return pd.DataFrame(summary)

def get_tier_features(featured_data, tier=1):
    """Get specific tier features for Excel export"""
    if tier == 1:
        features = ['log_return_1d', 'volatility_20d', 'price_vs_ma20', 'return_momentum_5d', 'daily_range']
    elif tier == 2:
        features = ['volatility_ratio', 'price_vs_ma50', 'ma20_vs_ma50', 'price_percentile_30d', 'return_acceleration']
    elif tier == 3:
        features = ['z_score_20d', 'trend_persistence', 'volatility_momentum', 'return_skewness_10d', 'return_persistence']
    else:
        return pd.DataFrame()
    
    available_features = [f for f in features if f in featured_data.columns]
    return featured_data[available_features]

def create_market_analysis(featured_data):
    """Create market analysis summary for Excel"""
    latest = featured_data.iloc[-1]
    
    # Market condition interpretations
    analysis = []
    
    # Price trend analysis
    if latest['price_vs_ma20'] > 5:
        trend_analysis = "Strong uptrend - Price well above 20-day MA"
    elif latest['price_vs_ma20'] > 0:
        trend_analysis = "Mild uptrend - Price above 20-day MA"
    elif latest['price_vs_ma20'] > -5:
        trend_analysis = "Mild downtrend - Price below 20-day MA"
    else:
        trend_analysis = "Strong downtrend - Price well below 20-day MA"
    
    # Volatility analysis
    if latest['volatility_regime'] == 2:
        vol_analysis = "High volatility environment - Expect large price swings"
    elif latest['volatility_regime'] == 1:
        vol_analysis = "Medium volatility - Normal market conditions"
    else:
        vol_analysis = "Low volatility - Calm market conditions"
    
    # Momentum analysis
    if latest['return_persistence'] > 0.8:
        momentum_analysis = "Strong momentum - Trend likely to continue"
    elif latest['return_persistence'] > 0.6:
        momentum_analysis = "Moderate momentum - Some directional bias"
    else:
        momentum_analysis = "Weak momentum - Choppy/sideways movement"
    
    # Risk analysis
    if latest['return_skewness_10d'] < -0.5:
        risk_analysis = "Elevated crash risk - Recent negative skew in returns"
    elif latest['return_skewness_10d'] > 0.5:
        risk_analysis = "Upside bias - Recent positive skew in returns"
    else:
        risk_analysis = "Balanced risk - Symmetric return distribution"
    
    analysis = [
        {'Category': 'Price Trend', 'Analysis': trend_analysis, 'Value': f"{latest['price_vs_ma20']:.2f}%"},
        {'Category': 'Volatility', 'Analysis': vol_analysis, 'Value': f"{latest['volatility_20d']:.1f}%"},
        {'Category': 'Momentum', 'Analysis': momentum_analysis, 'Value': f"{latest['return_persistence']:.3f}"},
        {'Category': 'Risk Profile', 'Analysis': risk_analysis, 'Value': f"{latest['return_skewness_10d']:.3f}"},
    ]
    
    return pd.DataFrame(analysis)

# Complete workflow function
def download_bitcoin_excel(start_date='2020-01-01', end_date=None, filename=None):
    """
    Complete workflow: Fetch Bitcoin data, calculate features, save to Excel
    
    Parameters:
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    filename (str): Output filename (None for auto-generated)
    
    Returns:
    tuple: (featured_data, filename)
    """
    print("🚀 Starting Bitcoin Data Download & Feature Engineering...")
    print("="*60)
    
    # Step 1: Fetch Bitcoin data
    print("📊 Step 1: Fetching Bitcoin data from Yahoo Finance...")
    btc_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    
    if btc_data is None or btc_data.empty:
        print("❌ Failed to fetch Bitcoin data")
        return None, None
    
    # Step 2: Calculate features
    print("\n🔧 Step 2: Calculating all price-based features...")
    try:
        feature_engine = BitcoinFeatureEngine(btc_data)
        featured_data = feature_engine.calculate_all_features()
        
        # Ensure no timezone issues
        if featured_data.index.tz is not None:
            featured_data.index = featured_data.index.tz_localize(None)
            
    except Exception as e:
        print(f"❌ Error calculating features: {e}")
        return None, None
    
    # Step 3: Save to Excel
    print(f"\n💾 Step 3: Saving to Excel...")
    saved_filename = save_to_excel(featured_data, filename=filename, include_summary=True)
    
    if saved_filename:
        print(f"\n✅ COMPLETE! Bitcoin data saved to: {saved_filename}")
        
        # Show quick summary
        print(f"\n📋 QUICK SUMMARY:")
        print(f"📅 Data Period: {featured_data.index.min().date()} to {featured_data.index.max().date()}")
        print(f"📊 Total Records: {len(featured_data):,}")
        print(f"🔢 Total Features: {len(featured_data.columns)}")
        print(f"💰 Current Price: ${featured_data['close'].iloc[-1]:,.2f}")
        print(f"📈 30-day Return: {((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%")
        
        return featured_data, saved_filename
    else:
        return featured_data, None

# Quick download functions for different periods
def download_bitcoin_1year(filename=None):
    """Download last 1 year of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=1)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_1year.xlsx'
    )

def download_bitcoin_2years(filename=None):
    """Download last 2 years of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=2)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_2years.xlsx'
    )

def download_bitcoin_5years(filename=None):
    """Download last 5 years of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=5)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_5years.xlsx'
    )

def download_bitcoin_max(filename=None):
    """Download maximum available Bitcoin data"""
    return download_bitcoin_excel(
        start_date='2010-01-01',
        filename=filename or 'bitcoin_max_data.xlsx'
    )

if __name__ == "__main__":
    # Example usage - Download Bitcoin data to single Excel sheet
    print("🚀 Bitcoin Feature Engineering & Single Sheet Excel Export")
    print("=" * 60)
    
    # Option 1: Download last 2 years to single sheet with analysis
    print("\n📊 Downloading last 2 years of Bitcoin data to single sheet...")
    data, filename = download_bitcoin_2years_single()
    
    # Option 2: Custom date range to single sheet
    # data, filename = download_bitcoin_excel_single(
    #     start_date='2023-01-01', 
    #     end_date='2024-12-31',
    #     filename='bitcoin_2023_2024_single.xlsx'
    # )
    
    # Option 3: Maximum data available to single sheet
    # data, filename = download_bitcoin_max_single()
    
    # Option 4: Without interpretation columns (just raw data + features)
    # data, filename = download_bitcoin_excel_single(
    #     start_date='2022-01-01',
    #     filename='bitcoin_raw_features.xlsx',
    #     include_analysis=False
    # )
    
    if filename:
        print(f"\n🎉 Success! Check your file: {filename}")
        print(f"\n📊 File contains:")
        print(f"  • Date column (YYYY-MM-DD format)")
        print(f"  • OHLCV price data")
        print(f"  • 45+ technical features")
        print(f"  • Interpretation columns for key features")
        print(f"  • Professional Excel formatting")
    else:
        print("\n❌ Download failed!")

import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class BitcoinFeatureEngine:
    """
    Comprehensive feature engineering for Bitcoin price prediction
    Calculates all price-based features from the documentation
    """
    
    def __init__(self, df):
        """
        Initialize with OHLCV dataframe
        Expected columns: ['open', 'high', 'low', 'close', 'volume']
        Index should be datetime
        """
        self.df = df.copy()
        self.df.columns = self.df.columns.str.lower()
        required_cols = ['open', 'high', 'low', 'close']
        
        if not all(col in self.df.columns for col in required_cols):
            raise ValueError(f"DataFrame must contain columns: {required_cols}")
    
    def calculate_all_features(self):
        """Calculate all price-based features"""
        print("Calculating Bitcoin price-based features...")
        
        # 1. Returns Features
        self._calculate_returns()
        
        # 2. Volatility Features  
        self._calculate_volatility()
        
        # 3. Moving Average Features
        self._calculate_ma_features()
        
        # 4. High-Low Range Features
        self._calculate_range_features()
        
        # 5. Price Position and Momentum
        self._calculate_position_momentum()
        
        # 6. Advanced Features
        self._calculate_advanced_features()
        
        return self.df
    
    def _calculate_returns(self):
        """Calculate return-based features"""
        print("  - Returns features...")
        
        # Basic returns
        self.df['simple_return_1d'] = self.df['close'].pct_change()
        self.df['log_return_1d'] = np.log(self.df['close'] / self.df['close'].shift(1))
        
        # Multi-period returns
        self.df['log_return_3d'] = self.df['log_return_1d'].rolling(3).sum()
        self.df['log_return_7d'] = self.df['log_return_1d'].rolling(7).sum()
        self.df['log_return_30d'] = self.df['log_return_1d'].rolling(30).sum()
        
        # Return derivatives
        self.df['return_momentum_5d'] = self.df['log_return_1d'].rolling(5).mean()
        self.df['return_acceleration'] = self.df['log_return_1d'].diff()
        
        # Return volatility ratio
        rolling_vol = self.df['log_return_1d'].rolling(20).std()
        self.df['return_volatility_ratio'] = self.df['log_return_1d'] / rolling_vol
        
        # Return skewness (KEY FEATURE)
        self.df['return_skewness_10d'] = self.df['log_return_1d'].rolling(10).apply(
            lambda x: stats.skew(x.dropna()) if len(x.dropna()) >= 3 else np.nan
        )
        
        # Return persistence (KEY FEATURE)
        self.df['return_persistence'] = self._calculate_return_persistence()
        
        # Risk-adjusted returns
        self.df['sharpe_10d'] = (self.df['return_momentum_5d'] * np.sqrt(252)) / (rolling_vol * np.sqrt(252))
        
        # Return autocorrelation
        self.df['return_autocorr_5d'] = self.df['log_return_1d'].rolling(10).apply(
            lambda x: x.autocorr(lag=1) if len(x.dropna()) > 5 else np.nan
        )
    
    def _calculate_return_persistence(self):
        """Calculate return persistence - consistency of return direction"""
        returns = self.df['log_return_1d']
        
        # Method 1: Directional consistency ratio (5-day window)
        return_signs = np.sign(returns)
        consistency = return_signs.rolling(5).apply(
            lambda x: len(x[x == x.iloc[-1]]) / len(x) if len(x) > 0 else np.nan
        )
        
        return consistency
    
    def _calculate_volatility(self):
        """Calculate volatility-based features"""
        print("  - Volatility features...")
        
        returns = self.df['log_return_1d']
        
        # Historical volatility (different windows)
        self.df['volatility_5d'] = returns.rolling(5).std() * np.sqrt(252)
        self.df['volatility_20d'] = returns.rolling(20).std() * np.sqrt(252)
        self.df['volatility_60d'] = returns.rolling(60).std() * np.sqrt(252)
        
        # Parkinson volatility (high-low based)
        try:
            self.df['parkinson_vol'] = np.sqrt(
                np.log(self.df['high'] / self.df['low']).rolling(20).apply(
                    lambda x: (x**2).mean() / (4 * np.log(2)) if len(x) > 0 else np.nan
                )
            ) * np.sqrt(252)
        except:
            self.df['parkinson_vol'] = self.df['volatility_20d']  # Fallback
        
        # Garman-Klass volatility
        try:
            self.df['garman_klass_vol'] = self._calculate_garman_klass()
        except:
            self.df['garman_klass_vol'] = self.df['volatility_20d']  # Fallback
        
        # Volatility relationships
        self.df['volatility_ratio'] = self.df['volatility_5d'] / self.df['volatility_60d']
        self.df['volatility_momentum'] = self.df['volatility_20d'].pct_change(5)
        
        # Volatility regime classification (fixed)
        vol_20d = self.df['volatility_20d']
        try:
            vol_low = vol_20d.rolling(252).quantile(0.33)
            vol_high = vol_20d.rolling(252).quantile(0.67)
            
            # Create volatility regime: 0=Low, 1=Medium, 2=High
            conditions = [
                vol_20d <= vol_low,
                (vol_20d > vol_low) & (vol_20d <= vol_high),
                vol_20d > vol_high
            ]
            self.df['volatility_regime'] = np.select(conditions, [0, 1, 2], default=np.nan)
        except:
            # Simple fallback regime classification
            vol_median = vol_20d.rolling(252).median()
            self.df['volatility_regime'] = np.where(vol_20d > vol_median, 1, 0)
        
        # Volatility mean reversion
        vol_mean = self.df['volatility_20d'].rolling(252).mean()
        self.df['volatility_mean_reversion'] = (self.df['volatility_20d'] - vol_mean) / vol_mean
        
        # Advanced volatility features
        try:
            self.df['volatility_clustering'] = self._calculate_vol_clustering()
        except:
            self.df['volatility_clustering'] = self.df['volatility_20d'].rolling(10).std()
            
        self.df['vol_of_vol'] = self.df['volatility_20d'].rolling(20).std()
    
    def _calculate_garman_klass(self):
        """Calculate Garman-Klass volatility estimator"""
        o, h, l, c = self.df['open'], self.df['high'], self.df['low'], self.df['close']
        
        term1 = 0.5 * (np.log(h/l))**2
        term2 = (2*np.log(2) - 1) * (np.log(c/o))**2
        
        gk_vol = (term1 - term2).rolling(20).mean()
        return np.sqrt(gk_vol * 252)
    
    def _calculate_vol_clustering(self):
        """Measure volatility clustering/persistence"""
        vol_changes = self.df['volatility_5d'].pct_change().abs()
        return vol_changes.rolling(10).std()
    
    def _calculate_ma_features(self):
        """Calculate moving average features"""
        print("  - Moving average features...")
        
        close = self.df['close']
        
        # Moving averages
        self.df['ma20'] = close.rolling(20).mean()
        self.df['ma50'] = close.rolling(50).mean()
        self.df['ma200'] = close.rolling(200).mean()
        
        # Distance from MAs (percentage)
        self.df['price_vs_ma20'] = (close - self.df['ma20']) / self.df['ma20'] * 100
        self.df['price_vs_ma50'] = (close - self.df['ma50']) / self.df['ma50'] * 100
        self.df['price_vs_ma200'] = (close - self.df['ma200']) / self.df['ma200'] * 100
        
        # MA relationships
        self.df['ma20_vs_ma50'] = (self.df['ma20'] - self.df['ma50']) / self.df['ma50'] * 100
        self.df['ma50_vs_ma200'] = (self.df['ma50'] - self.df['ma200']) / self.df['ma200'] * 100
        
        # MA convergence
        ma_spread = np.abs(self.df['ma20'] - self.df['ma50']) + np.abs(self.df['ma50'] - self.df['ma200'])
        self.df['ma_convergence'] = ma_spread / close
        
        # MA slopes (momentum)
        self.df['ma20_slope'] = self.df['ma20'].pct_change(5) * 100
        self.df['ma50_slope'] = self.df['ma50'].pct_change(5) * 100
        self.df['ma_acceleration'] = self.df['ma20_slope'].diff()
        
        # Binary position features
        self.df['price_above_ma20'] = (close > self.df['ma20']).astype(int)
        self.df['price_above_ma50'] = (close > self.df['ma50']).astype(int)
        self.df['price_above_all_mas'] = (
            (close > self.df['ma20']) & 
            (close > self.df['ma50']) & 
            (close > self.df['ma200'])
        ).astype(int)
        
        # Bullish alignment
        self.df['ma_bullish_alignment'] = (
            (self.df['ma20'] > self.df['ma50']) & 
            (self.df['ma50'] > self.df['ma200'])
        ).astype(int)
    
    def _calculate_range_features(self):
        """Calculate high-low range features"""
        print("  - Range features...")
        
        o, h, l, c = self.df['open'], self.df['high'], self.df['low'], self.df['close']
        prev_close = c.shift(1)
        
        # Daily range indicators
        self.df['daily_range'] = (h - l) / c * 100
        avg_range = self.df['daily_range'].rolling(20).mean()
        self.df['range_vs_average'] = self.df['daily_range'] / avg_range
        self.df['range_position'] = (c - l) / (h - l)
        self.df['gap_open'] = (o - prev_close) / prev_close * 100
        
        # True Range and ATR
        tr1 = h - l
        tr2 = np.abs(h - prev_close)
        tr3 = np.abs(l - prev_close)
        self.df['true_range'] = np.maximum(tr1, np.maximum(tr2, tr3))
        self.df['average_true_range'] = self.df['true_range'].rolling(14).mean()
        
        # Range expansion/contraction
        range_ma = self.df['daily_range'].rolling(10).mean()
        self.df['range_expansion'] = self.df['daily_range'] / range_ma
        self.df['range_contraction'] = 1 / self.df['range_expansion']
    
    def _calculate_position_momentum(self):
        """Calculate price position and momentum features"""
        print("  - Position and momentum features...")
        
        close = self.df['close']
        
        # Price percentiles
        self.df['price_percentile_30d'] = close.rolling(30).rank(pct=True) * 100
        self.df['price_percentile_90d'] = close.rolling(90).rank(pct=True) * 100
        
        # Distance from highs/lows
        rolling_high_30d = close.rolling(30).max()
        rolling_low_30d = close.rolling(30).min()
        self.df['distance_from_high'] = (rolling_high_30d - close) / close * 100
        self.df['distance_from_low'] = (close - rolling_low_30d) / close * 100
        
        # Momentum features
        self.df['price_momentum_10d'] = close.pct_change(10) * 100
        
        # RSI-style momentum
        delta = close.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(14).mean()
        avg_loss = loss.rolling(14).mean()
        rs = avg_gain / avg_loss
        self.df['rsi'] = 100 - (100 / (1 + rs))
        
        # Momentum persistence
        momentum_sign = np.sign(self.df['price_momentum_10d'])
        self.df['momentum_persistence'] = momentum_sign.rolling(5).apply(
            lambda x: (x == x.iloc[-1]).sum() / len(x) if len(x) > 0 else np.nan
        )
    
    def _calculate_advanced_features(self):
        """Calculate advanced price-based features"""
        print("  - Advanced features...")
        
        close = self.df['close']
        returns = self.df['log_return_1d']
        
        # Z-scores (mean reversion indicators)
        rolling_mean_20d = close.rolling(20).mean()
        rolling_std_20d = close.rolling(20).std()
        self.df['z_score_20d'] = (close - rolling_mean_20d) / rolling_std_20d
        
        rolling_mean_60d = close.rolling(60).mean()
        rolling_std_60d = close.rolling(60).std()
        self.df['z_score_60d'] = (close - rolling_mean_60d) / rolling_std_60d
        
        # Bollinger Bands position
        bb_upper = rolling_mean_20d + (2 * rolling_std_20d)
        bb_lower = rolling_mean_20d - (2 * rolling_std_20d)
        self.df['bollinger_position'] = (close - bb_lower) / (bb_upper - bb_lower)
        
        # Mean reversion pressure
        long_term_mean = close.rolling(252).mean()
        self.df['mean_reversion_pressure'] = (close - long_term_mean) / long_term_mean * 100
        
        # Trend quality features
        self.df['trend_strength'] = self._calculate_trend_strength()
        self.df['trend_persistence'] = self._calculate_trend_persistence()
        
        # Market microstructure approximations
        self.df['noise_ratio'] = self._calculate_noise_ratio()
        
        # Hurst exponent (trend vs mean reversion tendency)
        self.df['hurst_exponent'] = returns.rolling(50).apply(
            lambda x: self._calculate_hurst(x.dropna()) if len(x.dropna()) > 10 else np.nan
        )
    
    def _calculate_trend_strength(self):
        """Calculate trend strength based on price consistency"""
        close = self.df['close']
        trend_up = (close > close.shift(1)).rolling(10).sum() / 10
        trend_down = (close < close.shift(1)).rolling(10).sum() / 10
        return np.maximum(trend_up, trend_down)
    
    def _calculate_trend_persistence(self):
        """Calculate how long trends persist"""
        price_changes = np.sign(self.df['close'].diff())
        
        # Calculate run lengths
        runs = []
        current_run = 1
        
        for i in range(1, len(price_changes)):
            if price_changes.iloc[i] == price_changes.iloc[i-1]:
                current_run += 1
            else:
                runs.append(current_run)
                current_run = 1
        
        # Rolling average of recent run lengths
        trend_persistence = pd.Series(index=self.df.index, dtype=float)
        window = 20
        
        for i in range(window, len(self.df)):
            recent_runs = [r for r in runs if r > 0][-window:]
            if recent_runs:
                trend_persistence.iloc[i] = np.mean(recent_runs)
        
        return trend_persistence
    
    def _calculate_noise_ratio(self):
        """Estimate noise vs signal in price movements"""
        returns = self.df['log_return_1d']
        
        # Compare actual volatility to theoretical random walk
        actual_vol = returns.rolling(20).std()
        
        # Theoretical vol if purely random
        daily_returns = returns.rolling(20).apply(lambda x: np.mean(np.abs(x)))
        theoretical_vol = daily_returns * np.sqrt(np.pi/2)  # For random walk
        
        return actual_vol / theoretical_vol
    
    def _calculate_hurst(self, returns):
        """Calculate Hurst exponent for trend vs mean reversion"""
        if len(returns) < 10:
            return np.nan
            
        try:
            # Simple R/S calculation
            n = len(returns)
            mean_return = np.mean(returns)
            
            # Cumulative deviations
            cum_devs = np.cumsum(returns - mean_return)
            
            # Range
            R = np.max(cum_devs) - np.min(cum_devs)
            
            # Standard deviation
            S = np.std(returns)
            
            if S == 0:
                return 0.5
                
            # R/S ratio
            rs_ratio = R / S
            
            # Hurst exponent approximation
            if rs_ratio > 0:
                return np.log(rs_ratio) / np.log(n)
            else:
                return 0.5
                
        except:
            return np.nan
    
    def get_tier1_features(self):
        """Return Tier 1 (essential) features"""
        tier1_features = [
            'log_return_1d',
            'volatility_20d', 
            'price_vs_ma20',
            'return_momentum_5d',
            'daily_range'
        ]
        
        available_features = [f for f in tier1_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_tier2_features(self):
        """Return Tier 2 (high value) features"""
        tier2_features = [
            'volatility_ratio',
            'price_vs_ma50',
            'ma20_vs_ma50', 
            'price_percentile_30d',
            'return_acceleration'
        ]
        
        available_features = [f for f in tier2_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_tier3_features(self):
        """Return Tier 3 (advanced) features"""
        tier3_features = [
            'z_score_20d',
            'trend_persistence',
            'volatility_momentum',
            'return_skewness_10d',
            'return_persistence'
        ]
        
        available_features = [f for f in tier3_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_feature_summary(self):
        """Get summary of all calculated features"""
        feature_cols = [col for col in self.df.columns 
                       if col not in ['open', 'high', 'low', 'close', 'volume']]
        
        summary = {}
        for col in feature_cols:
            summary[col] = {
                'count': self.df[col].count(),
                'mean': self.df[col].mean(),
                'std': self.df[col].std(),
                'min': self.df[col].min(),
                'max': self.df[col].max()
            }
        
        return pd.DataFrame(summary).T


# Debug function to check date handling
def debug_date_handling(btc_data):
    """Debug function to check how dates are being processed"""
    print("\n🔍 DEBUG: Date Handling Information")
    print("="*50)
    print(f"Original yfinance data index type: {type(btc_data.index)}")
    print(f"Original yfinance data index dtype: {btc_data.index.dtype}")
    print(f"Has timezone: {btc_data.index.tz is not None}")
    if btc_data.index.tz:
        print(f"Timezone: {btc_data.index.tz}")
    
    print(f"\nFirst 5 dates from yfinance:")
    for i, date in enumerate(btc_data.index[:5]):
        print(f"  {i+1}. {date} (type: {type(date)})")
    
    print(f"\nLast 5 dates from yfinance:")
    for i, date in enumerate(btc_data.index[-5:]):
        print(f"  {i+1}. {date} (type: {type(date)})")
    
    return True

# Bitcoin Data Fetcher with improved date handling
def fetch_bitcoin_data(start_date='2020-01-01', end_date=None, period='max', debug=False):
    """
    Fetch Bitcoin data from Yahoo Finance with proper date handling
    
    Parameters:
    start_date (str): Start date in 'YYYY-MM-DD' format
    end_date (str): End date in 'YYYY-MM-DD' format (None for today)
    period (str): Period for data ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
    debug (bool): Whether to print debug information about date handling
    
    Returns:
    pandas.DataFrame: Bitcoin OHLCV data
    """
    try:
        import yfinance as yf
        print("Fetching Bitcoin data from Yahoo Finance...")
        
        # Bitcoin ticker symbol
        btc_ticker = "BTC-USD"
        
        # Create ticker object
        btc = yf.Ticker(btc_ticker)
        
        # Fetch historical data
        if start_date and end_date:
            btc_data = btc.history(start=start_date, end=end_date, interval='1d')
        elif start_date:
            btc_data = btc.history(start=start_date, interval='1d')
        else:
            btc_data = btc.history(period=period, interval='1d')
        
        if btc_data.empty:
            raise ValueError("No data retrieved. Check your date range or internet connection.")
        
        if debug:
            debug_date_handling(btc_data)
        
        # Clean column names and select required columns
        btc_data.columns = btc_data.columns.str.lower()
        btc_data = btc_data[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # Remove timezone from datetime index but preserve the dates (Excel compatibility)
        if btc_data.index.tz is not None:
            print(f"📅 Removing timezone from dates (was: {btc_data.index.tz})")
            btc_data.index = btc_data.index.tz_localize(None)
        
        # Ensure index is datetime
        if not isinstance(btc_data.index, pd.DatetimeIndex):
            btc_data.index = pd.to_datetime(btc_data.index)
        
        # Remove any rows with missing data
        btc_data = btc_data.dropna()
        
        print(f"✅ Successfully fetched {len(btc_data)} days of Bitcoin data")
        print(f"📅 Date range: {btc_data.index.min().strftime('%Y-%m-%d')} to {btc_data.index.max().strftime('%Y-%m-%d')}")
        print(f"💰 Price range: ${btc_data['close'].min():.2f} to ${btc_data['close'].max():.2f}")
        
        return btc_data
        
    except ImportError:
        print("❌ yfinance not installed. Installing now...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "yfinance"])
        
        # Try again after installation
        import yfinance as yf
        return fetch_bitcoin_data(start_date, end_date, period, debug)
        
    except Exception as e:
        print(f"❌ Error fetching Bitcoin data: {e}")
        print("Using sample data instead...")
        return create_sample_data()

def create_sample_data():
    """Create sample Bitcoin data as fallback"""
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', '2024-01-01', freq='D')
    
    # Simulate realistic Bitcoin price data
    initial_price = 10000
    returns = np.random.normal(0.001, 0.04, len(dates))
    prices = [initial_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Create OHLCV data
    bitcoin_data = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.02))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.02))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(1000, 10000, len(dates))
    }, index=dates)
    
    # Ensure high >= close >= low and high >= open >= low
    bitcoin_data['high'] = bitcoin_data[['open', 'close', 'high']].max(axis=1)
    bitcoin_data['low'] = bitcoin_data[['open', 'close', 'low']].min(axis=1)
    
    return bitcoin_data

# Example usage and testing
def demo_usage(use_real_data=True, start_date='2020-01-01', end_date=None):
    """
    Demonstrate how to use the feature engine with real Bitcoin data
    
    Parameters:
    use_real_data (bool): Whether to fetch real data from Yahoo Finance
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    """
    
    if use_real_data:
        # Fetch real Bitcoin data from Yahoo Finance
        bitcoin_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    else:
        # Use sample data
        bitcoin_data = create_sample_data()
        print("Using sample Bitcoin data")
    
    print("\n📊 Bitcoin OHLCV Data Preview:")
    print(bitcoin_data.head())
    print(f"\n📈 Data shape: {bitcoin_data.shape}")
    
    # Basic statistics
    print(f"\n💹 Current Bitcoin Price: ${bitcoin_data['close'].iloc[-1]:,.2f}")
    print(f"📊 30-day return: {((bitcoin_data['close'].iloc[-1] / bitcoin_data['close'].iloc[-30]) - 1) * 100:.2f}%")
    print(f"📊 YTD return: {((bitcoin_data['close'].iloc[-1] / bitcoin_data['close'].iloc[0]) - 1) * 100:.2f}%")
    
    # Initialize feature engine
    print(f"\n🔧 Initializing Bitcoin Feature Engine...")
    feature_engine = BitcoinFeatureEngine(bitcoin_data)
    
    # Calculate all features
    featured_data = feature_engine.calculate_all_features()
    
    print(f"\n✅ Features calculated! New shape: {featured_data.shape}")
    print(f"📊 Total features created: {featured_data.shape[1] - 5}")  # Subtract OHLCV columns
    
    # Show key features
    print("\n" + "="*50)
    print("🎯 KEY FEATURES ANALYSIS")
    print("="*50)
    
    print("\n📈 Return Skewness (10-day) - Last 10 days:")
    skew_data = featured_data['return_skewness_10d'].tail(10)
    for date, value in skew_data.items():
        if not pd.isna(value):
            interpretation = "📉 Crash risk" if value < -0.5 else "📈 Upside bias" if value > 0.5 else "⚖️ Balanced"
            print(f"  {date.date()}: {value:.3f} {interpretation}")
    
    print("\n🔄 Return Persistence - Last 10 days:")
    persist_data = featured_data['return_persistence'].tail(10)
    for date, value in persist_data.items():
        if not pd.isna(value):
            interpretation = "🚀 Strong trend" if value > 0.8 else "📊 Choppy" if value < 0.6 else "📈 Moderate trend"
            print(f"  {date.date()}: {value:.3f} {interpretation}")
    
    # Current market conditions
    latest = featured_data.iloc[-1]
    print(f"\n🔍 CURRENT MARKET CONDITIONS:")
    print(f"💰 Price vs 20-day MA: {latest['price_vs_ma20']:.2f}%")
    print(f"📊 20-day Volatility: {latest['volatility_20d']:.1f}%")
    print(f"📈 10-day Momentum: {latest['price_momentum_10d']:.2f}%")
    print(f"🎯 30-day Price Percentile: {latest['price_percentile_30d']:.1f}%")
    
    # Get feature tiers
    print("\n" + "="*50)
    print("📊 TIER 1 FEATURES (Essential)")
    print("="*50)
    tier1 = feature_engine.get_tier1_features()
    print(tier1.tail(3))
    
    print("\n" + "="*50)
    print("📈 TIER 2 FEATURES (High Value)")
    print("="*50)
    tier2 = feature_engine.get_tier2_features()
    print(tier2.tail(3))
    
    print("\n" + "="*50)
    print("🔬 TIER 3 FEATURES (Advanced)")
    print("="*50)
    tier3 = feature_engine.get_tier3_features()
    print(tier3.tail(3))
    
    # Feature correlation analysis
    print("\n📊 Feature Correlation with Future Returns:")
    future_return = featured_data['log_return_1d'].shift(-1)  # Next day return
    
    key_features = ['return_skewness_10d', 'return_persistence', 'volatility_ratio', 
                   'price_vs_ma20', 'z_score_20d']
    
    correlations = {}
    for feature in key_features:
        if feature in featured_data.columns:
            corr = featured_data[feature].corr(future_return)
            correlations[feature] = corr
            print(f"  {feature}: {corr:.3f}")
    
    return featured_data, feature_engine

# Quick analysis function
def quick_bitcoin_analysis(days_back=30):
    """Quick Bitcoin analysis for recent data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.Timedelta(days=days_back + 200)  # Extra days for feature calculation
    
    btc_data = fetch_bitcoin_data(start_date=start_date.strftime('%Y-%m-%d'))
    engine = BitcoinFeatureEngine(btc_data)
    featured_data = engine.calculate_all_features()
    
    # Focus on recent data
    recent_data = featured_data.tail(days_back)
    
    print(f"\n🔍 BITCOIN ANALYSIS - Last {days_back} Days")
    print("="*50)
    
    latest = recent_data.iloc[-1]
    
    print(f"💰 Current Price: ${latest['close']:,.2f}")
    print(f"📊 Daily Return: {latest['log_return_1d']*100:.2f}%")
    print(f"📈 Return Skewness: {latest['return_skewness_10d']:.3f}")
    print(f"🔄 Return Persistence: {latest['return_persistence']:.3f}")
    print(f"⚡ Volatility Regime: {latest['volatility_regime']:.0f} (0=Low, 1=Med, 2=High)")
    print(f"🎯 Trend Strength: {latest['trend_strength']:.3f}")
    
    return recent_data

# Excel Export Functions - Single Sheet Version
def save_to_excel_single_sheet(featured_data, filename=None, include_analysis=True):
    """
    Save Bitcoin data with features to a single Excel worksheet
    
    Parameters:
    featured_data (pd.DataFrame): DataFrame with all features
    filename (str): Output filename (None for auto-generated name)
    include_analysis (bool): Whether to include analysis columns
    
    Returns:
    str: Path to saved file
    """
    try:
        if filename is None:
            # Auto-generate filename with current date
            current_date = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            filename = f'bitcoin_features_{current_date}.xlsx'
        
        print(f"💾 Saving Bitcoin data to single Excel sheet: {filename}")
        
        # Prepare the data
        export_data = featured_data.copy()
        
        # Remove timezone from index if present (Excel compatibility) but preserve date format
        if export_data.index.tz is not None:
            export_data.index = export_data.index.tz_localize(None)
        
        # Reset index to make date a column - preserve original date format
        export_data = export_data.reset_index()
        
        # Ensure the date column is properly named and formatted
        if 'index' in export_data.columns:
            export_data.rename(columns={'index': 'Date'}, inplace=True)
        elif export_data.index.name:
            export_data.rename(columns={export_data.index.name: 'Date'}, inplace=True)
        else:
            export_data.rename(columns={export_data.columns[0]: 'Date'}, inplace=True)
        
        # Ensure Date column is datetime type
        if 'Date' in export_data.columns:
            export_data['Date'] = pd.to_datetime(export_data['Date'])
            print(f"✅ Date column preserved: {export_data['Date'].dtype}")
            print(f"📅 Date range: {export_data['Date'].min()} to {export_data['Date'].max()}")
        
        if include_analysis:
            # Add interpretation columns for key features
            export_data = add_interpretation_columns(export_data)
        
        # Create Excel writer with formatting options
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            
            # Save to single sheet
            export_data.to_excel(writer, sheet_name='Bitcoin_Data_Features', 
                               index=False, startrow=0)
            
            # Get the worksheet for formatting
            worksheet = writer.sheets['Bitcoin_Data_Features']
            
            # Format the worksheet
            format_excel_worksheet(worksheet, export_data)
            
            print("✅ Bitcoin data with all features saved to single sheet")
        
        print(f"\n🎉 Successfully saved Bitcoin data to: {filename}")
        print(f"📊 Total rows: {len(export_data)}")
        print(f"📈 Total columns: {len(export_data.columns)}")
        print(f"📅 Date range: {export_data['Date'].min().date()} to {export_data['Date'].max().date()}")
        
        return filename
        
    except Exception as e:
        print(f"❌ Error saving to Excel: {e}")
        return None

def add_interpretation_columns(df):
    """Add interpretation columns for key features"""
    
    # Return Skewness Interpretation
    def interpret_skewness(value):
        if pd.isna(value):
            return ""
        elif value < -0.5:
            return "Crash Risk"
        elif value > 0.5:
            return "Upside Bias"
        else:
            return "Balanced"
    
    # Return Persistence Interpretation
    def interpret_persistence(value):
        if pd.isna(value):
            return ""
        elif value > 0.8:
            return "Strong Trend"
        elif value < 0.6:
            return "Choppy"
        else:
            return "Moderate Trend"
    
    # Volatility Regime Interpretation
    def interpret_vol_regime(value):
        if pd.isna(value):
            return ""
        elif value == 0:
            return "Low Vol"
        elif value == 1:
            return "Medium Vol"
        else:
            return "High Vol"
    
    # Price vs MA20 Interpretation
    def interpret_price_vs_ma(value):
        if pd.isna(value):
            return ""
        elif value > 5:
            return "Strong Uptrend"
        elif value > 0:
            return "Uptrend"
        elif value > -5:
            return "Downtrend"
        else:
            return "Strong Downtrend"
    
    # RSI Interpretation
    def interpret_rsi(value):
        if pd.isna(value):
            return ""
        elif value > 70:
            return "Overbought"
        elif value < 30:
            return "Oversold"
        else:
            return "Neutral"
    
    # Add interpretation columns after their respective feature columns
    
    # Find column positions
    col_positions = {}
    for i, col in enumerate(df.columns):
        col_positions[col] = i
    
    # Add interpretations
    if 'return_skewness_10d' in df.columns:
        pos = col_positions['return_skewness_10d'] + 1
        df.insert(pos, 'skewness_interpretation', 
                 df['return_skewness_10d'].apply(interpret_skewness))
    
    if 'return_persistence' in df.columns:
        pos = col_positions.get('skewness_interpretation', col_positions['return_persistence']) + 1
        df.insert(pos, 'persistence_interpretation', 
                 df['return_persistence'].apply(interpret_persistence))
    
    if 'volatility_regime' in df.columns:
        pos = col_positions.get('persistence_interpretation', col_positions['volatility_regime']) + 1
        df.insert(pos, 'vol_regime_interpretation', 
                 df['volatility_regime'].apply(interpret_vol_regime))
    
    if 'price_vs_ma20' in df.columns:
        pos = col_positions.get('vol_regime_interpretation', col_positions['price_vs_ma20']) + 1
        df.insert(pos, 'trend_interpretation', 
                 df['price_vs_ma20'].apply(interpret_price_vs_ma))
    
    if 'rsi' in df.columns:
        pos = col_positions.get('trend_interpretation', col_positions['rsi']) + 1
        df.insert(pos, 'rsi_interpretation', 
                 df['rsi'].apply(interpret_rsi))
    
    return df

def format_excel_worksheet(worksheet, df):
    """Format the Excel worksheet for better readability"""
    try:
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        from openpyxl.utils import get_column_letter
        
        # Header formatting
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        # Apply header formatting
        for col in range(1, len(df.columns) + 1):
            cell = worksheet.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center")
        
        # Date column formatting - more robust
        if 'Date' in df.columns:
            date_col_idx = df.columns.get_loc('Date') + 1
            date_col_letter = get_column_letter(date_col_idx)
            
            # Format all date cells
            for row in range(2, len(df) + 2):
                cell = worksheet[f"{date_col_letter}{row}"]
                cell.number_format = 'YYYY-MM-DD'
                # Ensure it's treated as a date
                if hasattr(cell.value, 'date'):
                    cell.value = cell.value.date() if hasattr(cell.value, 'date') else cell.value
            
            print(f"✅ Date column formatted: Column {date_col_letter}")
        
        # Set Date column width appropriately
        if 'Date' in df.columns:
            date_col_letter = get_column_letter(df.columns.get_loc('Date') + 1)
            worksheet.column_dimensions[date_col_letter].width = 12
        
        # Price columns formatting (numeric only, no currency symbols)
        price_cols = ['open', 'high', 'low', 'close']
        for col_name in price_cols:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = get_column_letter(col_idx)
                for row in range(2, len(df) + 2):
                    cell = worksheet[f"{col_letter}{row}"]
                    cell.number_format = '#,##0.00'  # Number format without $ symbol
        
        # Volume formatting (whole numbers)
        if 'volume' in df.columns:
            vol_col_idx = df.columns.get_loc('volume') + 1
            vol_col_letter = get_column_letter(vol_col_idx)
            for row in range(2, len(df) + 2):
                cell = worksheet[f"{vol_col_letter}{row}"]
                cell.number_format = '#,##0'  # Whole numbers with commas
        
        # Percentage columns formatting
        pct_cols = [col for col in df.columns if 'return' in col.lower() or 'vs_ma' in col or 'volatility' in col]
        for col_name in pct_cols:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = get_column_letter(col_idx)
                for row in range(2, len(df) + 2):
                    cell = worksheet[f"{col_letter}{row}"]
                    if 'volatility' in col_name:
                        cell.number_format = '0.00%'
                    else:
                        cell.number_format = '0.000'
        
        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 20)  # Max width of 20
            worksheet.column_dimensions[column_letter].width = adjusted_width
        
        # Freeze the header row
        worksheet.freeze_panes = "A2"
        
        print("✅ Excel formatting applied successfully")
        
    except ImportError:
        print("⚠️ openpyxl formatting not available - basic export completed")
    except Exception as e:
        print(f"⚠️ Excel formatting error: {e} - basic export completed")

# Updated download function for single sheet
def download_bitcoin_excel_single(start_date='2020-01-01', end_date=None, filename=None, include_analysis=True):
    """
    Complete workflow: Fetch Bitcoin data, calculate features, save to single Excel sheet
    
    Parameters:
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    filename (str): Output filename (None for auto-generated)
    include_analysis (bool): Whether to include interpretation columns
    
    Returns:
    tuple: (featured_data, filename)
    """
    print("🚀 Starting Bitcoin Data Download & Feature Engineering...")
    print("="*60)
    
    # Step 1: Fetch Bitcoin data
    print("📊 Step 1: Fetching Bitcoin data from Yahoo Finance...")
    btc_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    
    if btc_data is None or btc_data.empty:
        print("❌ Failed to fetch Bitcoin data")
        return None, None
    
    # Step 2: Calculate features
    print("\n🔧 Step 2: Calculating all price-based features...")
    try:
        feature_engine = BitcoinFeatureEngine(btc_data)
        featured_data = feature_engine.calculate_all_features()
        
        # Ensure no timezone issues
        if featured_data.index.tz is not None:
            featured_data.index = featured_data.index.tz_localize(None)
            
    except Exception as e:
        print(f"❌ Error calculating features: {e}")
        return None, None
    
    # Step 3: Save to single Excel sheet
    print(f"\n💾 Step 3: Saving to single Excel sheet...")
    saved_filename = save_to_excel_single_sheet(featured_data, filename=filename, include_analysis=include_analysis)
    
    if saved_filename:
        print(f"\n✅ COMPLETE! Bitcoin data saved to: {saved_filename}")
        
        # Show quick summary
        print(f"\n📋 QUICK SUMMARY:")
        print(f"📅 Data Period: {featured_data.index.min().date()} to {featured_data.index.max().date()}")
        print(f"📊 Total Records: {len(featured_data):,}")
        print(f"🔢 Total Features: {len(featured_data.columns)}")
        print(f"💰 Current Price: ${featured_data['close'].iloc[-1]:,.2f}")
        print(f"📈 30-day Return: {((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%")
        
        return featured_data, saved_filename
    else:
        return featured_data, None

# Updated quick download functions for single sheet
def download_bitcoin_1year_single(filename=None):
    """Download last 1 year of Bitcoin data to single sheet"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=1)
    return download_bitcoin_excel_single(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_1year_single.xlsx'
    )

def download_bitcoin_2years_single(filename=None):
    """Download last 2 years of Bitcoin data to single sheet"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=2)
    return download_bitcoin_excel_single(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_2years_single.xlsx'
    )

def download_bitcoin_5years_single(filename=None):
    """Download last 5 years of Bitcoin data to single sheet"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=5)
    return download_bitcoin_excel_single(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_5years_single.xlsx'
    )

def download_bitcoin_max_single(filename=None):
    """Download maximum available Bitcoin data to single sheet"""
    return download_bitcoin_excel_single(
        start_date='2010-01-01',
        filename=filename or 'bitcoin_max_data_single.xlsx'
    )

def create_excel_summary(featured_data):
    """Create summary statistics for Excel export"""
    latest = featured_data.iloc[-1]
    
    summary = {
        'Metric': [
            'Current Price',
            'Daily Return (%)',
            '30-day Return (%)',
            'YTD Return (%)',
            '20-day Volatility (%)',
            'Return Skewness (10d)',
            'Return Persistence',
            'Price vs MA20 (%)',
            'Price vs MA50 (%)',
            'Volatility Regime',
            'Trend Strength',
            'RSI',
            '30-day Price Percentile',
            'Z-Score (20d)'
        ],
        'Value': [
            f"${latest['close']:,.2f}",
            f"{latest['log_return_1d']*100:.2f}%",
            f"{((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%",
            f"{((featured_data['close'].iloc[-1] / featured_data['close'].iloc[0]) - 1) * 100:.2f}%",
            f"{latest['volatility_20d']:.1f}%",
            f"{latest['return_skewness_10d']:.3f}",
            f"{latest['return_persistence']:.3f}",
            f"{latest['price_vs_ma20']:.2f}%",
            f"{latest['price_vs_ma50']:.2f}%",
            f"{latest['volatility_regime']:.0f}",
            f"{latest['trend_strength']:.3f}",
            f"{latest['rsi']:.1f}",
            f"{latest['price_percentile_30d']:.1f}%",
            f"{latest['z_score_20d']:.3f}"
        ],
        'Interpretation': [
            'Current Bitcoin price',
            'Today\'s price change',
            'Monthly performance',
            'Year-to-date performance',
            'Risk/uncertainty level',
            'Asymmetry in returns',
            'Directional consistency',
            'Short-term trend position',
            'Medium-term trend position',
            '0=Low, 1=Med, 2=High vol',
            'Trend consistency (0-1)',
            'Momentum indicator (0-100)',
            'Relative price position',
            'Distance from mean'
        ]
    }
    
    return pd.DataFrame(summary)

def get_tier_features(featured_data, tier=1):
    """Get specific tier features for Excel export"""
    if tier == 1:
        features = ['log_return_1d', 'volatility_20d', 'price_vs_ma20', 'return_momentum_5d', 'daily_range']
    elif tier == 2:
        features = ['volatility_ratio', 'price_vs_ma50', 'ma20_vs_ma50', 'price_percentile_30d', 'return_acceleration']
    elif tier == 3:
        features = ['z_score_20d', 'trend_persistence', 'volatility_momentum', 'return_skewness_10d', 'return_persistence']
    else:
        return pd.DataFrame()
    
    available_features = [f for f in features if f in featured_data.columns]
    return featured_data[available_features]

def create_market_analysis(featured_data):
    """Create market analysis summary for Excel"""
    latest = featured_data.iloc[-1]
    
    # Market condition interpretations
    analysis = []
    
    # Price trend analysis
    if latest['price_vs_ma20'] > 5:
        trend_analysis = "Strong uptrend - Price well above 20-day MA"
    elif latest['price_vs_ma20'] > 0:
        trend_analysis = "Mild uptrend - Price above 20-day MA"
    elif latest['price_vs_ma20'] > -5:
        trend_analysis = "Mild downtrend - Price below 20-day MA"
    else:
        trend_analysis = "Strong downtrend - Price well below 20-day MA"
    
    # Volatility analysis
    if latest['volatility_regime'] == 2:
        vol_analysis = "High volatility environment - Expect large price swings"
    elif latest['volatility_regime'] == 1:
        vol_analysis = "Medium volatility - Normal market conditions"
    else:
        vol_analysis = "Low volatility - Calm market conditions"
    
    # Momentum analysis
    if latest['return_persistence'] > 0.8:
        momentum_analysis = "Strong momentum - Trend likely to continue"
    elif latest['return_persistence'] > 0.6:
        momentum_analysis = "Moderate momentum - Some directional bias"
    else:
        momentum_analysis = "Weak momentum - Choppy/sideways movement"
    
    # Risk analysis
    if latest['return_skewness_10d'] < -0.5:
        risk_analysis = "Elevated crash risk - Recent negative skew in returns"
    elif latest['return_skewness_10d'] > 0.5:
        risk_analysis = "Upside bias - Recent positive skew in returns"
    else:
        risk_analysis = "Balanced risk - Symmetric return distribution"
    
    analysis = [
        {'Category': 'Price Trend', 'Analysis': trend_analysis, 'Value': f"{latest['price_vs_ma20']:.2f}%"},
        {'Category': 'Volatility', 'Analysis': vol_analysis, 'Value': f"{latest['volatility_20d']:.1f}%"},
        {'Category': 'Momentum', 'Analysis': momentum_analysis, 'Value': f"{latest['return_persistence']:.3f}"},
        {'Category': 'Risk Profile', 'Analysis': risk_analysis, 'Value': f"{latest['return_skewness_10d']:.3f}"},
    ]
    
    return pd.DataFrame(analysis)

# Complete workflow function
def download_bitcoin_excel(start_date='2020-01-01', end_date=None, filename=None):
    """
    Complete workflow: Fetch Bitcoin data, calculate features, save to Excel
    
    Parameters:
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    filename (str): Output filename (None for auto-generated)
    
    Returns:
    tuple: (featured_data, filename)
    """
    print("🚀 Starting Bitcoin Data Download & Feature Engineering...")
    print("="*60)
    
    # Step 1: Fetch Bitcoin data
    print("📊 Step 1: Fetching Bitcoin data from Yahoo Finance...")
    btc_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    
    if btc_data is None or btc_data.empty:
        print("❌ Failed to fetch Bitcoin data")
        return None, None
    
    # Step 2: Calculate features
    print("\n🔧 Step 2: Calculating all price-based features...")
    try:
        feature_engine = BitcoinFeatureEngine(btc_data)
        featured_data = feature_engine.calculate_all_features()
        
        # Ensure no timezone issues
        if featured_data.index.tz is not None:
            featured_data.index = featured_data.index.tz_localize(None)
            
    except Exception as e:
        print(f"❌ Error calculating features: {e}")
        return None, None
    
    # Step 3: Save to Excel
    print(f"\n💾 Step 3: Saving to Excel...")
    saved_filename = save_to_excel(featured_data, filename=filename, include_summary=True)
    
    if saved_filename:
        print(f"\n✅ COMPLETE! Bitcoin data saved to: {saved_filename}")
        
        # Show quick summary
        print(f"\n📋 QUICK SUMMARY:")
        print(f"📅 Data Period: {featured_data.index.min().date()} to {featured_data.index.max().date()}")
        print(f"📊 Total Records: {len(featured_data):,}")
        print(f"🔢 Total Features: {len(featured_data.columns)}")
        print(f"💰 Current Price: ${featured_data['close'].iloc[-1]:,.2f}")
        print(f"📈 30-day Return: {((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%")
        
        return featured_data, saved_filename
    else:
        return featured_data, None

# Quick download functions for different periods
def download_bitcoin_1year(filename=None):
    """Download last 1 year of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=1)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_1year.xlsx'
    )

def download_bitcoin_2years(filename=None):
    """Download last 2 years of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=2)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_2years.xlsx'
    )

def download_bitcoin_5years(filename=None):
    """Download last 5 years of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=5)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_5years.xlsx'
    )

def download_bitcoin_max(filename=None):
    """Download maximum available Bitcoin data"""
    return download_bitcoin_excel(
        start_date='2010-01-01',
        filename=filename or 'bitcoin_max_data.xlsx'
    )

# Quick test function to verify date handling
def test_bitcoin_dates():
    """Quick test to verify date handling is working correctly"""
    print("🧪 TESTING: Bitcoin Date Handling")
    print("="*40)
    
    # Fetch small amount of data for testing
    test_data = fetch_bitcoin_data(start_date='2024-05-01', end_date='2024-05-10', debug=True)
    
    if test_data is not None:
        print(f"\n📊 Test data shape: {test_data.shape}")
        print(f"📅 Index type: {type(test_data.index)}")
        print(f"📅 Index dtype: {test_data.index.dtype}")
        
        # Test the Excel export with small dataset
        filename = 'test_bitcoin_dates.xlsx'
        export_data = test_data.copy()
        export_data = export_data.reset_index()
        
        if 'index' in export_data.columns:
            export_data.rename(columns={'index': 'Date'}, inplace=True)
        
        print(f"\n📋 Export data columns: {list(export_data.columns)}")
        print(f"📅 Date column type: {export_data['Date'].dtype}")
        print(f"\nFirst 3 dates in export:")
        for i, date in enumerate(export_data['Date'].head(3)):
            print(f"  {i+1}. {date} (type: {type(date)})")
        
        # Try saving to Excel
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                export_data.to_excel(writer, sheet_name='Test_Dates', index=False)
            print(f"\n✅ Test Excel file saved: {filename}")
            return True
        except Exception as e:
            print(f"\n❌ Test Excel save failed: {e}")
            return False
    
    return False

if __name__ == "__main__":
    # Example usage - Download Bitcoin data to single Excel sheet
    print("🚀 Bitcoin Feature Engineering & Single Sheet Excel Export")
    print("=" * 60)
    
    # Uncomment this line to test date handling first
    # test_bitcoin_dates()
    
    # Option 1: Download last 2 years to single sheet with analysis
    print("\n📊 Downloading last 2 years of Bitcoin data to single sheet...")
    data, filename = download_bitcoin_2years_single()
    
    # Option 2: Custom date range to single sheet
    # data, filename = download_bitcoin_excel_single(
    #     start_date='2023-01-01', 
    #     end_date='2024-12-31',
    #     filename='bitcoin_2023_2024_single.xlsx'
    # )
    
    # Option 3: Maximum data available to single sheet
    # data, filename = download_bitcoin_max_single()
    
    # Option 4: Without interpretation columns (just raw data + features)
    # data, filename = download_bitcoin_excel_single(
    #     start_date='2022-01-01',
    #     filename='bitcoin_raw_features.xlsx',
    #     include_analysis=False
    # )
    
    if filename:
        print(f"\n🎉 Success! Check your file: {filename}")
        print(f"\n📊 File contains:")
        print(f"  • Date column (YYYY-MM-DD format)")
        print(f"  • OHLCV price data")
        print(f"  • 45+ technical features")
        print(f"  • Interpretation columns for key features")
        print(f"  • Professional Excel formatting")
    else:
        print("\n❌ Download failed!")







import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class BitcoinFeatureEngine:
    """
    Comprehensive feature engineering for Bitcoin price prediction
    Calculates all price-based features from the documentation
    """
    
    def __init__(self, df):
        """
        Initialize with OHLCV dataframe
        Expected columns: ['open', 'high', 'low', 'close', 'volume']
        Index should be datetime
        """
        self.df = df.copy()
        self.df.columns = self.df.columns.str.lower()
        required_cols = ['open', 'high', 'low', 'close']
        
        if not all(col in self.df.columns for col in required_cols):
            raise ValueError(f"DataFrame must contain columns: {required_cols}")
    
    def calculate_all_features(self):
        """Calculate all price-based features"""
        print("Calculating Bitcoin price-based features...")
        
        # 1. Returns Features
        self._calculate_returns()
        
        # 2. Volatility Features  
        self._calculate_volatility()
        
        # 3. Moving Average Features
        self._calculate_ma_features()
        
        # 4. High-Low Range Features
        self._calculate_range_features()
        
        # 5. Price Position and Momentum
        self._calculate_position_momentum()
        
        # 6. Advanced Features
        self._calculate_advanced_features()
        
        return self.df
    
    def _calculate_returns(self):
        """Calculate return-based features"""
        print("  - Returns features...")
        
        # Basic returns
        self.df['simple_return_1d'] = self.df['close'].pct_change()
        self.df['log_return_1d'] = np.log(self.df['close'] / self.df['close'].shift(1))
        
        # Multi-period returns
        self.df['log_return_3d'] = self.df['log_return_1d'].rolling(3).sum()
        self.df['log_return_7d'] = self.df['log_return_1d'].rolling(7).sum()
        self.df['log_return_30d'] = self.df['log_return_1d'].rolling(30).sum()
        
        # Return derivatives
        self.df['return_momentum_5d'] = self.df['log_return_1d'].rolling(5).mean()
        self.df['return_acceleration'] = self.df['log_return_1d'].diff()
        
        # Return volatility ratio
        rolling_vol = self.df['log_return_1d'].rolling(20).std()
        self.df['return_volatility_ratio'] = self.df['log_return_1d'] / rolling_vol
        
        # Return skewness (KEY FEATURE)
        self.df['return_skewness_10d'] = self.df['log_return_1d'].rolling(10).apply(
            lambda x: stats.skew(x.dropna()) if len(x.dropna()) >= 3 else np.nan
        )
        
        # Return persistence (KEY FEATURE)
        self.df['return_persistence'] = self._calculate_return_persistence()
        
        # Risk-adjusted returns
        self.df['sharpe_10d'] = (self.df['return_momentum_5d'] * np.sqrt(252)) / (rolling_vol * np.sqrt(252))
        
        # Return autocorrelation
        self.df['return_autocorr_5d'] = self.df['log_return_1d'].rolling(10).apply(
            lambda x: x.autocorr(lag=1) if len(x.dropna()) > 5 else np.nan
        )
    
    def _calculate_return_persistence(self):
        """Calculate return persistence - consistency of return direction"""
        returns = self.df['log_return_1d']
        
        # Method 1: Directional consistency ratio (5-day window)
        return_signs = np.sign(returns)
        consistency = return_signs.rolling(5).apply(
            lambda x: len(x[x == x.iloc[-1]]) / len(x) if len(x) > 0 else np.nan
        )
        
        return consistency
    
    def _calculate_volatility(self):
        """Calculate volatility-based features"""
        print("  - Volatility features...")
        
        returns = self.df['log_return_1d']
        
        # Historical volatility (different windows)
        self.df['volatility_5d'] = returns.rolling(5).std() * np.sqrt(252)
        self.df['volatility_20d'] = returns.rolling(20).std() * np.sqrt(252)
        self.df['volatility_60d'] = returns.rolling(60).std() * np.sqrt(252)
        
        # Parkinson volatility (high-low based)
        try:
            self.df['parkinson_vol'] = np.sqrt(
                np.log(self.df['high'] / self.df['low']).rolling(20).apply(
                    lambda x: (x**2).mean() / (4 * np.log(2)) if len(x) > 0 else np.nan
                )
            ) * np.sqrt(252)
        except:
            self.df['parkinson_vol'] = self.df['volatility_20d']  # Fallback
        
        # Garman-Klass volatility
        try:
            self.df['garman_klass_vol'] = self._calculate_garman_klass()
        except:
            self.df['garman_klass_vol'] = self.df['volatility_20d']  # Fallback
        
        # Volatility relationships
        self.df['volatility_ratio'] = self.df['volatility_5d'] / self.df['volatility_60d']
        self.df['volatility_momentum'] = self.df['volatility_20d'].pct_change(5)
        
        # Volatility regime classification (fixed)
        vol_20d = self.df['volatility_20d']
        try:
            vol_low = vol_20d.rolling(252).quantile(0.33)
            vol_high = vol_20d.rolling(252).quantile(0.67)
            
            # Create volatility regime: 0=Low, 1=Medium, 2=High
            conditions = [
                vol_20d <= vol_low,
                (vol_20d > vol_low) & (vol_20d <= vol_high),
                vol_20d > vol_high
            ]
            self.df['volatility_regime'] = np.select(conditions, [0, 1, 2], default=np.nan)
        except:
            # Simple fallback regime classification
            vol_median = vol_20d.rolling(252).median()
            self.df['volatility_regime'] = np.where(vol_20d > vol_median, 1, 0)
        
        # Volatility mean reversion
        vol_mean = self.df['volatility_20d'].rolling(252).mean()
        self.df['volatility_mean_reversion'] = (self.df['volatility_20d'] - vol_mean) / vol_mean
        
        # Advanced volatility features
        try:
            self.df['volatility_clustering'] = self._calculate_vol_clustering()
        except:
            self.df['volatility_clustering'] = self.df['volatility_20d'].rolling(10).std()
            
        self.df['vol_of_vol'] = self.df['volatility_20d'].rolling(20).std()
        
        # NEW: Enhanced Volatility Interpretation Features
        self._add_volatility_interpretations()
    
    def _add_volatility_interpretations(self):
        """Add enhanced volatility interpretation columns"""
        print("    - Adding volatility interpretations...")
        
        # 1. Parkinson Volatility Interpretations
        self.df['parkinson_vol_level'] = self.df['parkinson_vol'].apply(self._interpret_parkinson_level)
        self.df['parkinson_vol_percentile'] = self.df['parkinson_vol'].rolling(252).rank(pct=True) * 100
        self.df['parkinson_vol_regime'] = self.df.apply(lambda row: self._classify_parkinson_regime(
            row['parkinson_vol'], row.get('parkinson_vol_percentile', 50)), axis=1)
        self.df['parkinson_trend_5d'] = self._calculate_volatility_trend(self.df['parkinson_vol'], 5)
        self.df['parkinson_vs_standard_ratio'] = self.df['parkinson_vol'] / self.df['volatility_20d']
        
        # 2. Garman-Klass Interpretations
        self.df['gk_vol_level'] = self.df['garman_klass_vol'].apply(self._interpret_gk_level)
        self.df['gk_vol_percentile'] = self.df['garman_klass_vol'].rolling(252).rank(pct=True) * 100
        self.df['gk_vs_parkinson_ratio'] = self.df['garman_klass_vol'] / self.df['parkinson_vol']
        self.df['gk_vol_quality'] = self.df.apply(lambda row: self._assess_gk_quality(
            row['garman_klass_vol'], row['parkinson_vol']), axis=1)
        self.df['gk_trend_direction'] = self._calculate_volatility_trend(self.df['garman_klass_vol'], 5)
        
        # 3. Volatility Ratio Enhancements
        self.df['vol_ratio_interpretation'] = self.df['volatility_ratio'].apply(self._interpret_vol_ratio)
        self.df['vol_ratio_percentile'] = self.df['volatility_ratio'].rolling(252).rank(pct=True) * 100
        self.df['vol_ratio_regime'] = self.df['volatility_ratio'].apply(self._classify_vol_ratio_regime)
        self.df['vol_ratio_signal'] = self.df['volatility_ratio'].apply(self._generate_vol_ratio_signal)
        self.df['vol_ratio_risk_level'] = self.df['volatility_ratio'].apply(self._assess_vol_ratio_risk)
        
        # 4. Volatility Percentile System
        self.df['vol_percentile_20d'] = self.df['volatility_20d'].rolling(60).rank(pct=True) * 100
        self.df['vol_percentile_60d'] = self.df['volatility_60d'].rolling(252).rank(pct=True) * 100
        self.df['vol_percentile_252d'] = self.df['volatility_20d'].rolling(1000).rank(pct=True) * 100
        self.df['vol_percentile_interpretation'] = self.df.apply(
            lambda row: self._interpret_vol_percentiles(row), axis=1)
        
        # 5. Volatility High/Low Analysis
        self._add_vol_high_low_analysis()
        
        # 6. Advanced Volatility Analysis
        self._add_advanced_vol_analysis()
        
        # 7. Composite Volatility Scores
        self._add_composite_vol_scores()
    
    def _interpret_parkinson_level(self, value):
        """Interpret Parkinson volatility level"""
        if pd.isna(value):
            return ""
        elif value < 25:
            return "Very Low"
        elif value < 45:
            return "Low"
        elif value < 65:
            return "Moderate"
        elif value < 85:
            return "High"
        elif value < 120:
            return "Very High"
        else:
            return "Extreme"
    
    def _classify_parkinson_regime(self, vol, percentile):
        """Classify Parkinson volatility regime"""
        if pd.isna(vol) or pd.isna(percentile):
            return ""
        
        if percentile < 20 and vol < 35:
            return "Compression"
        elif percentile < 50 and vol < 60:
            return "Normal"
        elif percentile < 80 and vol < 90:
            return "Elevated"
        else:
            return "Crisis"
    
    def _calculate_volatility_trend(self, vol_series, window):
        """Calculate volatility trend direction"""
        trend = vol_series.pct_change(window)
        
        def classify_trend(value):
            if pd.isna(value):
                return ""
            elif value > 0.3:
                return "Rising Fast"
            elif value > 0.1:
                return "Rising"
            elif value > -0.1:
                return "Stable"
            elif value > -0.3:
                return "Falling"
            else:
                return "Falling Fast"
        
        return trend.apply(classify_trend)
    
    def _interpret_gk_level(self, value):
        """Interpret Garman-Klass volatility level"""
        if pd.isna(value):
            return ""
        elif value < 23:
            return "Very Low"
        elif value < 43:
            return "Low"
        elif value < 63:
            return "Moderate"
        elif value < 83:
            return "High"
        elif value < 115:
            return "Very High"
        else:
            return "Extreme"
    
    def _assess_gk_quality(self, gk_vol, park_vol):
        """Assess quality of Garman-Klass volatility reading"""
        if pd.isna(gk_vol) or pd.isna(park_vol):
            return ""
        
        ratio = gk_vol / park_vol
        if 0.9 <= ratio <= 1.1:
            return "High Quality"
        elif 0.8 <= ratio <= 1.2:
            return "Good Quality"
        else:
            return "Low Quality"
    
    def _interpret_vol_ratio(self, value):
        """Interpret volatility ratio"""
        if pd.isna(value):
            return ""
        elif value < 0.5:
            return "Extreme Compression"
        elif value < 0.8:
            return "Compression"
        elif value < 1.2:
            return "Normal"
        elif value < 1.8:
            return "Expansion"
        else:
            return "Extreme Expansion"
    
    def _classify_vol_ratio_regime(self, value):
        """Classify volatility ratio regime"""
        if pd.isna(value):
            return ""
        elif value < 0.6:
            return "Deep Compression"
        elif value < 0.8:
            return "Building Compression"
        elif value < 1.2:
            return "Normal"
        elif value < 1.5:
            return "Early Expansion"
        elif value < 2.0:
            return "Full Expansion"
        else:
            return "Extreme Expansion"
    
    def _generate_vol_ratio_signal(self, value):
        """Generate trading signal from volatility ratio"""
        if pd.isna(value):
            return ""
        elif value < 0.6:
            return "Strong Breakout Setup"
        elif value < 0.8:
            return "Breakout Building"
        elif value < 1.2:
            return "Neutral"
        elif value < 1.8:
            return "Trend Acceleration"
        else:
            return "Extreme Conditions"
    
    def _assess_vol_ratio_risk(self, value):
        """Assess risk level from volatility ratio"""
        if pd.isna(value):
            return ""
        elif value < 0.7:
            return "Very Low Risk"
        elif value < 1.0:
            return "Low Risk"
        elif value < 1.3:
            return "Normal Risk"
        elif value < 1.8:
            return "High Risk"
        else:
            return "Extreme Risk"
    
    def _interpret_vol_percentiles(self, row):
        """Interpret combined volatility percentiles"""
        vol_20d_pct = row.get('vol_percentile_20d', 50)
        vol_60d_pct = row.get('vol_percentile_60d', 50)
        
        if pd.isna(vol_20d_pct):
            return ""
        
        avg_percentile = (vol_20d_pct + vol_60d_pct) / 2 if not pd.isna(vol_60d_pct) else vol_20d_pct
        
        if avg_percentile < 10:
            return "Bottom Decile"
        elif avg_percentile < 25:
            return "Low Quartile"
        elif avg_percentile < 50:
            return "Below Median"
        elif avg_percentile < 75:
            return "Above Median"
        elif avg_percentile < 90:
            return "Top Quartile"
        else:
            return "Top Decile"
    
    def _add_vol_high_low_analysis(self):
        """Add volatility high/low analysis columns"""
        vol_20d = self.df['volatility_20d']
        
        # Rolling high/low over 60 days
        vol_60d_high = vol_20d.rolling(60).max()
        vol_60d_low = vol_20d.rolling(60).min()
        
        # Near high/low flags
        self.df['vol_near_high_flag'] = vol_20d > (vol_60d_high * 0.9)
        self.df['vol_near_low_flag'] = vol_20d < (vol_60d_low * 1.1)
        
        # Days since high/low
        self.df['days_since_vol_high'] = self._calculate_days_since_extreme(vol_20d, vol_60d_high, 'high')
        self.df['days_since_vol_low'] = self._calculate_days_since_extreme(vol_20d, vol_60d_low, 'low')
        
        # Position between high/low
        self.df['vol_high_low_position'] = ((vol_20d - vol_60d_low) / (vol_60d_high - vol_60d_low) * 100).fillna(50)
        
        # Extreme event flag
        vol_95th = vol_20d.rolling(252).quantile(0.95)
        vol_5th = vol_20d.rolling(252).quantile(0.05)
        self.df['vol_extreme_event_flag'] = (vol_20d > vol_95th) | (vol_20d < vol_5th)
    
    def _calculate_days_since_extreme(self, current_series, extreme_series, extreme_type):
        """Calculate days since volatility extreme"""
        days_since = pd.Series(index=current_series.index, dtype=float)
        
        for i in range(len(current_series)):
            if pd.isna(extreme_series.iloc[i]):
                days_since.iloc[i] = np.nan
                continue
                
            # Look back to find when we hit the extreme
            lookback_window = min(60, i)
            if lookback_window == 0:
                days_since.iloc[i] = np.nan
                continue
                
            recent_data = current_series.iloc[i-lookback_window:i+1]
            extreme_value = extreme_series.iloc[i]
            
            if extreme_type == 'high':
                extreme_indices = recent_data[recent_data >= extreme_value * 0.99].index
            else:
                extreme_indices = recent_data[recent_data <= extreme_value * 1.01].index
            
            if len(extreme_indices) > 0:
                last_extreme_idx = extreme_indices[-1]
                days_since.iloc[i] = (current_series.index[i] - last_extreme_idx).days
            else:
                days_since.iloc[i] = lookback_window
        
        return days_since
    
    def _add_advanced_vol_analysis(self):
        """Add advanced volatility analysis columns"""
        vol_20d = self.df['volatility_20d']
        
        # Volatility clustering strength
        vol_high_threshold = vol_20d.rolling(252).quantile(0.75)
        high_vol_periods = (vol_20d > vol_high_threshold).astype(int)
        self.df['vol_clustering_strength'] = high_vol_periods.rolling(10).sum() / 10
        
        # Mean reversion signal
        vol_252d_mean = vol_20d.rolling(252).mean()
        vol_distance = (vol_20d - vol_252d_mean) / vol_252d_mean
        self.df['vol_mean_reversion_signal'] = vol_distance.apply(self._classify_mean_reversion)
        
        # Breakout probability (simplified)
        vol_ratio = self.df['volatility_ratio']
        compression_strength = (1 - vol_ratio).clip(0, 1)
        self.df['vol_breakout_probability'] = (compression_strength * 100).round(1)
        
        # Regime stability
        vol_std = vol_20d.rolling(20).std()
        vol_mean_20d = vol_20d.rolling(20).mean()
        stability_ratio = vol_std / vol_mean_20d
        self.df['vol_regime_stability'] = stability_ratio.apply(self._classify_regime_stability)
        
        # Forward indicator (simplified trend)
        vol_momentum = self.df['volatility_momentum']
        self.df['vol_forward_indicator'] = vol_momentum.apply(self._classify_forward_indicator)
    
    def _classify_mean_reversion(self, value):
        """Classify mean reversion signal strength"""
        if pd.isna(value):
            return ""
        elif abs(value) > 0.5:
            return "Strong"
        elif abs(value) > 0.25:
            return "Moderate"
        else:
            return "Weak"
    
    def _classify_regime_stability(self, value):
        """Classify volatility regime stability"""
        if pd.isna(value):
            return ""
        elif value < 0.1:
            return "Very Stable"
        elif value < 0.2:
            return "Stable"
        elif value < 0.4:
            return "Transitioning"
        elif value < 0.6:
            return "Unstable"
        else:
            return "Chaotic"
    
    def _classify_forward_indicator(self, value):
        """Classify forward volatility indicator"""
        if pd.isna(value):
            return ""
        elif value > 0.1:
            return "Rising"
        elif value < -0.1:
            return "Falling"
        else:
            return "Stable"
    
    def _add_composite_vol_scores(self):
        """Add composite volatility scores"""
        vol_20d = self.df['volatility_20d']
        vol_ratio = self.df['volatility_ratio']
        vol_momentum = self.df['volatility_momentum']
        
        # Volatility Health Score (0-100)
        # Lower volatility + stable regime = healthier
        health_base = (100 - (vol_20d.clip(0, 150) / 150 * 100)).fillna(50)
        regime_adjustment = vol_ratio.apply(lambda x: 10 if 0.8 <= x <= 1.2 else -10 if x > 1.5 else 0)
        self.df['volatility_health_score'] = (health_base + regime_adjustment).clip(0, 100).round(1)
        
        # Opportunity Score (0-100)
        # Compression = high opportunity
        compression_score = (1 - vol_ratio.clip(0, 2) / 2) * 100
        momentum_bonus = vol_momentum.apply(lambda x: 20 if abs(x) > 0.2 else 0)
        self.df['volatility_opportunity_score'] = (compression_score + momentum_bonus).clip(0, 100).round(1)
        
        # Risk Score (0-100)
        # High volatility + expansion = high risk
        vol_risk = (vol_20d.clip(0, 150) / 150 * 100).fillna(50)
        expansion_penalty = vol_ratio.apply(lambda x: 30 if x > 1.5 else 0)
        self.df['volatility_risk_score'] = (vol_risk + expansion_penalty).clip(0, 100).round(1)
    
    def _calculate_garman_klass(self):
        """Calculate Garman-Klass volatility estimator"""
        o, h, l, c = self.df['open'], self.df['high'], self.df['low'], self.df['close']
        
        term1 = 0.5 * (np.log(h/l))**2
        term2 = (2*np.log(2) - 1) * (np.log(c/o))**2
        
        gk_vol = (term1 - term2).rolling(20).mean()
        return np.sqrt(gk_vol * 252)
    
    def _calculate_vol_clustering(self):
        """Measure volatility clustering/persistence"""
        vol_changes = self.df['volatility_5d'].pct_change().abs()
        return vol_changes.rolling(10).std()
    
    def _calculate_ma_features(self):
        """Calculate moving average features"""
        print("  - Moving average features...")
        
        close = self.df['close']
        
        # Moving averages
        self.df['ma20'] = close.rolling(20).mean()
        self.df['ma50'] = close.rolling(50).mean()
        self.df['ma200'] = close.rolling(200).mean()
        
        # Distance from MAs (percentage)
        self.df['price_vs_ma20'] = (close - self.df['ma20']) / self.df['ma20'] * 100
        self.df['price_vs_ma50'] = (close - self.df['ma50']) / self.df['ma50'] * 100
        self.df['price_vs_ma200'] = (close - self.df['ma200']) / self.df['ma200'] * 100
        
        # MA relationships
        self.df['ma20_vs_ma50'] = (self.df['ma20'] - self.df['ma50']) / self.df['ma50'] * 100
        self.df['ma50_vs_ma200'] = (self.df['ma50'] - self.df['ma200']) / self.df['ma200'] * 100
        
        # MA convergence
        ma_spread = np.abs(self.df['ma20'] - self.df['ma50']) + np.abs(self.df['ma50'] - self.df['ma200'])
        self.df['ma_convergence'] = ma_spread / close
        
        # MA slopes (momentum)
        self.df['ma20_slope'] = self.df['ma20'].pct_change(5) * 100
        self.df['ma50_slope'] = self.df['ma50'].pct_change(5) * 100
        self.df['ma_acceleration'] = self.df['ma20_slope'].diff()
        
        # Binary position features
        self.df['price_above_ma20'] = (close > self.df['ma20']).astype(int)
        self.df['price_above_ma50'] = (close > self.df['ma50']).astype(int)
        self.df['price_above_all_mas'] = (
            (close > self.df['ma20']) & 
            (close > self.df['ma50']) & 
            (close > self.df['ma200'])
        ).astype(int)
        
        # Bullish alignment
        self.df['ma_bullish_alignment'] = (
            (self.df['ma20'] > self.df['ma50']) & 
            (self.df['ma50'] > self.df['ma200'])
        ).astype(int)
    
    def _calculate_range_features(self):
        """Calculate high-low range features"""
        print("  - Range features...")
        
        o, h, l, c = self.df['open'], self.df['high'], self.df['low'], self.df['close']
        prev_close = c.shift(1)
        
        # Daily range indicators
        self.df['daily_range'] = (h - l) / c * 100
        avg_range = self.df['daily_range'].rolling(20).mean()
        self.df['range_vs_average'] = self.df['daily_range'] / avg_range
        self.df['range_position'] = (c - l) / (h - l)
        self.df['gap_open'] = (o - prev_close) / prev_close * 100
        
        # True Range and ATR
        tr1 = h - l
        tr2 = np.abs(h - prev_close)
        tr3 = np.abs(l - prev_close)
        self.df['true_range'] = np.maximum(tr1, np.maximum(tr2, tr3))
        self.df['average_true_range'] = self.df['true_range'].rolling(14).mean()
        
        # Range expansion/contraction
        range_ma = self.df['daily_range'].rolling(10).mean()
        self.df['range_expansion'] = self.df['daily_range'] / range_ma
        self.df['range_contraction'] = 1 / self.df['range_expansion']
    
    def _calculate_position_momentum(self):
        """Calculate price position and momentum features"""
        print("  - Position and momentum features...")
        
        close = self.df['close']
        
        # Price percentiles
        self.df['price_percentile_30d'] = close.rolling(30).rank(pct=True) * 100
        self.df['price_percentile_90d'] = close.rolling(90).rank(pct=True) * 100
        
        # Distance from highs/lows
        rolling_high_30d = close.rolling(30).max()
        rolling_low_30d = close.rolling(30).min()
        self.df['distance_from_high'] = (rolling_high_30d - close) / close * 100
        self.df['distance_from_low'] = (close - rolling_low_30d) / close * 100
        
        # Momentum features
        self.df['price_momentum_10d'] = close.pct_change(10) * 100
        
        # RSI-style momentum
        delta = close.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.rolling(14).mean()
        avg_loss = loss.rolling(14).mean()
        rs = avg_gain / avg_loss
        self.df['rsi'] = 100 - (100 / (1 + rs))
        
        # Momentum persistence
        momentum_sign = np.sign(self.df['price_momentum_10d'])
        self.df['momentum_persistence'] = momentum_sign.rolling(5).apply(
            lambda x: (x == x.iloc[-1]).sum() / len(x) if len(x) > 0 else np.nan
        )
    
    def _calculate_advanced_features(self):
        """Calculate advanced price-based features"""
        print("  - Advanced features...")
        
        close = self.df['close']
        returns = self.df['log_return_1d']
        
        # Z-scores (mean reversion indicators)
        rolling_mean_20d = close.rolling(20).mean()
        rolling_std_20d = close.rolling(20).std()
        self.df['z_score_20d'] = (close - rolling_mean_20d) / rolling_std_20d
        
        rolling_mean_60d = close.rolling(60).mean()
        rolling_std_60d = close.rolling(60).std()
        self.df['z_score_60d'] = (close - rolling_mean_60d) / rolling_std_60d
        
        # Bollinger Bands position
        bb_upper = rolling_mean_20d + (2 * rolling_std_20d)
        bb_lower = rolling_mean_20d - (2 * rolling_std_20d)
        self.df['bollinger_position'] = (close - bb_lower) / (bb_upper - bb_lower)
        
        # Mean reversion pressure
        long_term_mean = close.rolling(252).mean()
        self.df['mean_reversion_pressure'] = (close - long_term_mean) / long_term_mean * 100
        
        # Trend quality features
        self.df['trend_strength'] = self._calculate_trend_strength()
        self.df['trend_persistence'] = self._calculate_trend_persistence()
        
        # Market microstructure approximations
        self.df['noise_ratio'] = self._calculate_noise_ratio()
        
        # Hurst exponent (trend vs mean reversion tendency)
        self.df['hurst_exponent'] = returns.rolling(50).apply(
            lambda x: self._calculate_hurst(x.dropna()) if len(x.dropna()) > 10 else np.nan
        )
    
    def _calculate_trend_strength(self):
        """Calculate trend strength based on price consistency"""
        close = self.df['close']
        trend_up = (close > close.shift(1)).rolling(10).sum() / 10
        trend_down = (close < close.shift(1)).rolling(10).sum() / 10
        return np.maximum(trend_up, trend_down)
    
    def _calculate_trend_persistence(self):
        """Calculate how long trends persist"""
        price_changes = np.sign(self.df['close'].diff())
        
        # Calculate run lengths
        runs = []
        current_run = 1
        
        for i in range(1, len(price_changes)):
            if price_changes.iloc[i] == price_changes.iloc[i-1]:
                current_run += 1
            else:
                runs.append(current_run)
                current_run = 1
        
        # Rolling average of recent run lengths
        trend_persistence = pd.Series(index=self.df.index, dtype=float)
        window = 20
        
        for i in range(window, len(self.df)):
            recent_runs = [r for r in runs if r > 0][-window:]
            if recent_runs:
                trend_persistence.iloc[i] = np.mean(recent_runs)
        
        return trend_persistence
    
    def _calculate_noise_ratio(self):
        """Estimate noise vs signal in price movements"""
        returns = self.df['log_return_1d']
        
        # Compare actual volatility to theoretical random walk
        actual_vol = returns.rolling(20).std()
        
        # Theoretical vol if purely random
        daily_returns = returns.rolling(20).apply(lambda x: np.mean(np.abs(x)))
        theoretical_vol = daily_returns * np.sqrt(np.pi/2)  # For random walk
        
        return actual_vol / theoretical_vol
    
    def _calculate_hurst(self, returns):
        """Calculate Hurst exponent for trend vs mean reversion"""
        if len(returns) < 10:
            return np.nan
            
        try:
            # Simple R/S calculation
            n = len(returns)
            mean_return = np.mean(returns)
            
            # Cumulative deviations
            cum_devs = np.cumsum(returns - mean_return)
            
            # Range
            R = np.max(cum_devs) - np.min(cum_devs)
            
            # Standard deviation
            S = np.std(returns)
            
            if S == 0:
                return 0.5
                
            # R/S ratio
            rs_ratio = R / S
            
            # Hurst exponent approximation
            if rs_ratio > 0:
                return np.log(rs_ratio) / np.log(n)
            else:
                return 0.5
                
        except:
            return np.nan
    
    def get_tier1_features(self):
        """Return Tier 1 (essential) features"""
        tier1_features = [
            'log_return_1d',
            'volatility_20d', 
            'price_vs_ma20',
            'return_momentum_5d',
            'daily_range'
        ]
        
        available_features = [f for f in tier1_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_tier2_features(self):
        """Return Tier 2 (high value) features"""
        tier2_features = [
            'volatility_ratio',
            'price_vs_ma50',
            'ma20_vs_ma50', 
            'price_percentile_30d',
            'return_acceleration'
        ]
        
        available_features = [f for f in tier2_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_tier3_features(self):
        """Return Tier 3 (advanced) features"""
        tier3_features = [
            'z_score_20d',
            'trend_persistence',
            'volatility_momentum',
            'return_skewness_10d',
            'return_persistence'
        ]
        
        available_features = [f for f in tier3_features if f in self.df.columns]
        return self.df[available_features]
    
    def get_feature_summary(self):
        """Get summary of all calculated features"""
        feature_cols = [col for col in self.df.columns 
                       if col not in ['open', 'high', 'low', 'close', 'volume']]
        
        summary = {}
        for col in feature_cols:
            summary[col] = {
                'count': self.df[col].count(),
                'mean': self.df[col].mean(),
                'std': self.df[col].std(),
                'min': self.df[col].min(),
                'max': self.df[col].max()
            }
        
        return pd.DataFrame(summary).T


# Debug function to check date handling
def debug_date_handling(btc_data):
    """Debug function to check how dates are being processed"""
    print("\n🔍 DEBUG: Date Handling Information")
    print("="*50)
    print(f"Original yfinance data index type: {type(btc_data.index)}")
    print(f"Original yfinance data index dtype: {btc_data.index.dtype}")
    print(f"Has timezone: {btc_data.index.tz is not None}")
    if btc_data.index.tz:
        print(f"Timezone: {btc_data.index.tz}")
    
    print(f"\nFirst 5 dates from yfinance:")
    for i, date in enumerate(btc_data.index[:5]):
        print(f"  {i+1}. {date} (type: {type(date)})")
    
    print(f"\nLast 5 dates from yfinance:")
    for i, date in enumerate(btc_data.index[-5:]):
        print(f"  {i+1}. {date} (type: {type(date)})")
    
    return True

# Bitcoin Data Fetcher with improved date handling
def fetch_bitcoin_data(start_date='2020-01-01', end_date=None, period='max', debug=False):
    """
    Fetch Bitcoin data from Yahoo Finance with proper date handling
    
    Parameters:
    start_date (str): Start date in 'YYYY-MM-DD' format
    end_date (str): End date in 'YYYY-MM-DD' format (None for today)
    period (str): Period for data ('1d', '5d', '1mo', '3mo', '6mo', '1y', '2y', '5y', '10y', 'ytd', 'max')
    debug (bool): Whether to print debug information about date handling
    
    Returns:
    pandas.DataFrame: Bitcoin OHLCV data
    """
    try:
        import yfinance as yf
        print("Fetching Bitcoin data from Yahoo Finance...")
        
        # Bitcoin ticker symbol
        btc_ticker = "BTC-USD"
        
        # Create ticker object
        btc = yf.Ticker(btc_ticker)
        
        # Fetch historical data
        if start_date and end_date:
            btc_data = btc.history(start=start_date, end=end_date, interval='1d')
        elif start_date:
            btc_data = btc.history(start=start_date, interval='1d')
        else:
            btc_data = btc.history(period=period, interval='1d')
        
        if btc_data.empty:
            raise ValueError("No data retrieved. Check your date range or internet connection.")
        
        if debug:
            debug_date_handling(btc_data)
        
        # Clean column names and select required columns
        btc_data.columns = btc_data.columns.str.lower()
        btc_data = btc_data[['open', 'high', 'low', 'close', 'volume']].copy()
        
        # Remove timezone from datetime index but preserve the dates (Excel compatibility)
        if btc_data.index.tz is not None:
            print(f"📅 Removing timezone from dates (was: {btc_data.index.tz})")
            btc_data.index = btc_data.index.tz_localize(None)
        
        # Ensure index is datetime
        if not isinstance(btc_data.index, pd.DatetimeIndex):
            btc_data.index = pd.to_datetime(btc_data.index)
        
        # Remove any rows with missing data
        btc_data = btc_data.dropna()
        
        print(f"✅ Successfully fetched {len(btc_data)} days of Bitcoin data")
        print(f"📅 Date range: {btc_data.index.min().strftime('%Y-%m-%d')} to {btc_data.index.max().strftime('%Y-%m-%d')}")
        print(f"💰 Price range: ${btc_data['close'].min():.2f} to ${btc_data['close'].max():.2f}")
        
        return btc_data
        
    except ImportError:
        print("❌ yfinance not installed. Installing now...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "yfinance"])
        
        # Try again after installation
        import yfinance as yf
        return fetch_bitcoin_data(start_date, end_date, period, debug)
        
    except Exception as e:
        print(f"❌ Error fetching Bitcoin data: {e}")
        print("Using sample data instead...")
        return create_sample_data()

def create_sample_data():
    """Create sample Bitcoin data as fallback"""
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', '2024-01-01', freq='D')
    
    # Simulate realistic Bitcoin price data
    initial_price = 10000
    returns = np.random.normal(0.001, 0.04, len(dates))
    prices = [initial_price]
    
    for ret in returns[1:]:
        prices.append(prices[-1] * (1 + ret))
    
    # Create OHLCV data
    bitcoin_data = pd.DataFrame({
        'open': prices,
        'high': [p * (1 + abs(np.random.normal(0, 0.02))) for p in prices],
        'low': [p * (1 - abs(np.random.normal(0, 0.02))) for p in prices],
        'close': prices,
        'volume': np.random.uniform(1000, 10000, len(dates))
    }, index=dates)
    
    # Ensure high >= close >= low and high >= open >= low
    bitcoin_data['high'] = bitcoin_data[['open', 'close', 'high']].max(axis=1)
    bitcoin_data['low'] = bitcoin_data[['open', 'close', 'low']].min(axis=1)
    
    return bitcoin_data

# Example usage and testing
def demo_usage(use_real_data=True, start_date='2020-01-01', end_date=None):
    """
    Demonstrate how to use the feature engine with real Bitcoin data
    
    Parameters:
    use_real_data (bool): Whether to fetch real data from Yahoo Finance
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    """
    
    if use_real_data:
        # Fetch real Bitcoin data from Yahoo Finance
        bitcoin_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    else:
        # Use sample data
        bitcoin_data = create_sample_data()
        print("Using sample Bitcoin data")
    
    print("\n📊 Bitcoin OHLCV Data Preview:")
    print(bitcoin_data.head())
    print(f"\n📈 Data shape: {bitcoin_data.shape}")
    
    # Basic statistics
    print(f"\n💹 Current Bitcoin Price: ${bitcoin_data['close'].iloc[-1]:,.2f}")
    print(f"📊 30-day return: {((bitcoin_data['close'].iloc[-1] / bitcoin_data['close'].iloc[-30]) - 1) * 100:.2f}%")
    print(f"📊 YTD return: {((bitcoin_data['close'].iloc[-1] / bitcoin_data['close'].iloc[0]) - 1) * 100:.2f}%")
    
    # Initialize feature engine
    print(f"\n🔧 Initializing Bitcoin Feature Engine...")
    feature_engine = BitcoinFeatureEngine(bitcoin_data)
    
    # Calculate all features
    featured_data = feature_engine.calculate_all_features()
    
    print(f"\n✅ Features calculated! New shape: {featured_data.shape}")
    print(f"📊 Total features created: {featured_data.shape[1] - 5}")  # Subtract OHLCV columns
    
    # Show key features
    print("\n" + "="*50)
    print("🎯 KEY FEATURES ANALYSIS")
    print("="*50)
    
    print("\n📈 Return Skewness (10-day) - Last 10 days:")
    skew_data = featured_data['return_skewness_10d'].tail(10)
    for date, value in skew_data.items():
        if not pd.isna(value):
            interpretation = "📉 Crash risk" if value < -0.5 else "📈 Upside bias" if value > 0.5 else "⚖️ Balanced"
            print(f"  {date.date()}: {value:.3f} {interpretation}")
    
    print("\n🔄 Return Persistence - Last 10 days:")
    persist_data = featured_data['return_persistence'].tail(10)
    for date, value in persist_data.items():
        if not pd.isna(value):
            interpretation = "🚀 Strong trend" if value > 0.8 else "📊 Choppy" if value < 0.6 else "📈 Moderate trend"
            print(f"  {date.date()}: {value:.3f} {interpretation}")
    
    # Current market conditions
    latest = featured_data.iloc[-1]
    print(f"\n🔍 CURRENT MARKET CONDITIONS:")
    print(f"💰 Price vs 20-day MA: {latest['price_vs_ma20']:.2f}%")
    print(f"📊 20-day Volatility: {latest['volatility_20d']:.1f}%")
    print(f"📈 10-day Momentum: {latest['price_momentum_10d']:.2f}%")
    print(f"🎯 30-day Price Percentile: {latest['price_percentile_30d']:.1f}%")
    
    # Get feature tiers
    print("\n" + "="*50)
    print("📊 TIER 1 FEATURES (Essential)")
    print("="*50)
    tier1 = feature_engine.get_tier1_features()
    print(tier1.tail(3))
    
    print("\n" + "="*50)
    print("📈 TIER 2 FEATURES (High Value)")
    print("="*50)
    tier2 = feature_engine.get_tier2_features()
    print(tier2.tail(3))
    
    print("\n" + "="*50)
    print("🔬 TIER 3 FEATURES (Advanced)")
    print("="*50)
    tier3 = feature_engine.get_tier3_features()
    print(tier3.tail(3))
    
    # Feature correlation analysis
    print("\n📊 Feature Correlation with Future Returns:")
    future_return = featured_data['log_return_1d'].shift(-1)  # Next day return
    
    key_features = ['return_skewness_10d', 'return_persistence', 'volatility_ratio', 
                   'price_vs_ma20', 'z_score_20d']
    
    correlations = {}
    for feature in key_features:
        if feature in featured_data.columns:
            corr = featured_data[feature].corr(future_return)
            correlations[feature] = corr
            print(f"  {feature}: {corr:.3f}")
    
    return featured_data, feature_engine

# Quick analysis function
def quick_bitcoin_analysis(days_back=30):
    """Quick Bitcoin analysis for recent data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.Timedelta(days=days_back + 200)  # Extra days for feature calculation
    
    btc_data = fetch_bitcoin_data(start_date=start_date.strftime('%Y-%m-%d'))
    engine = BitcoinFeatureEngine(btc_data)
    featured_data = engine.calculate_all_features()
    
    # Focus on recent data
    recent_data = featured_data.tail(days_back)
    
    print(f"\n🔍 BITCOIN ANALYSIS - Last {days_back} Days")
    print("="*50)
    
    latest = recent_data.iloc[-1]
    
    print(f"💰 Current Price: ${latest['close']:,.2f}")
    print(f"📊 Daily Return: {latest['log_return_1d']*100:.2f}%")
    print(f"📈 Return Skewness: {latest['return_skewness_10d']:.3f}")
    print(f"🔄 Return Persistence: {latest['return_persistence']:.3f}")
    print(f"⚡ Volatility Regime: {latest['volatility_regime']:.0f} (0=Low, 1=Med, 2=High)")
    print(f"🎯 Trend Strength: {latest['trend_strength']:.3f}")
    
    return recent_data

# Excel Export Functions - Single Sheet Version
def save_to_excel_single_sheet(featured_data, filename=None, include_analysis=True):
    """
    Save Bitcoin data with features to a single Excel worksheet
    
    Parameters:
    featured_data (pd.DataFrame): DataFrame with all features
    filename (str): Output filename (None for auto-generated name)
    include_analysis (bool): Whether to include analysis columns
    
    Returns:
    str: Path to saved file
    """
    try:
        if filename is None:
            # Auto-generate filename with current date
            current_date = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            filename = f'bitcoin_features_{current_date}.xlsx'
        
        print(f"💾 Saving Bitcoin data to single Excel sheet: {filename}")
        
        # Prepare the data
        export_data = featured_data.copy()
        
        # Remove timezone from index if present (Excel compatibility) but preserve date format
        if export_data.index.tz is not None:
            export_data.index = export_data.index.tz_localize(None)
        
        # Reset index to make date a column - preserve original date format
        export_data = export_data.reset_index()
        
        # Ensure the date column is properly named and formatted
        if 'index' in export_data.columns:
            export_data.rename(columns={'index': 'Date'}, inplace=True)
        elif export_data.index.name:
            export_data.rename(columns={export_data.index.name: 'Date'}, inplace=True)
        else:
            export_data.rename(columns={export_data.columns[0]: 'Date'}, inplace=True)
        
        # Ensure Date column is datetime type
        if 'Date' in export_data.columns:
            export_data['Date'] = pd.to_datetime(export_data['Date'])
            print(f"✅ Date column preserved: {export_data['Date'].dtype}")
            print(f"📅 Date range: {export_data['Date'].min()} to {export_data['Date'].max()}")
        
        if include_analysis:
            # Add interpretation columns for key features
            export_data = add_interpretation_columns(export_data)
        
        # Create Excel writer with formatting options
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            
            # Save to single sheet
            export_data.to_excel(writer, sheet_name='Bitcoin_Data_Features', 
                               index=False, startrow=0)
            
            # Get the worksheet for formatting
            worksheet = writer.sheets['Bitcoin_Data_Features']
            
            # Format the worksheet
            format_excel_worksheet(worksheet, export_data)
            
            print("✅ Bitcoin data with all features saved to single sheet")
        
        print(f"\n🎉 Successfully saved Bitcoin data to: {filename}")
        print(f"📊 Total rows: {len(export_data)}")
        print(f"📈 Total columns: {len(export_data.columns)}")
        print(f"📅 Date range: {export_data['Date'].min().date()} to {export_data['Date'].max().date()}")
        
        return filename
        
    except Exception as e:
        print(f"❌ Error saving to Excel: {e}")
        return None

def add_interpretation_columns(df):
    """Add interpretation columns for key features"""
    
    # Return Skewness Interpretation
    def interpret_skewness(value):
        if pd.isna(value):
            return ""
        elif value < -0.5:
            return "Crash Risk"
        elif value > 0.5:
            return "Upside Bias"
        else:
            return "Balanced"
    
    # Return Persistence Interpretation
    def interpret_persistence(value):
        if pd.isna(value):
            return ""
        elif value > 0.8:
            return "Strong Trend"
        elif value < 0.6:
            return "Choppy"
        else:
            return "Moderate Trend"
    
    # Volatility Regime Interpretation
    def interpret_vol_regime(value):
        if pd.isna(value):
            return ""
        elif value == 0:
            return "Low Vol"
        elif value == 1:
            return "Medium Vol"
        else:
            return "High Vol"
    
    # Price vs MA20 Interpretation
    def interpret_price_vs_ma(value):
        if pd.isna(value):
            return ""
        elif value > 5:
            return "Strong Uptrend"
        elif value > 0:
            return "Uptrend"
        elif value > -5:
            return "Downtrend"
        else:
            return "Strong Downtrend"
    
    # RSI Interpretation
    def interpret_rsi(value):
        if pd.isna(value):
            return ""
        elif value > 70:
            return "Overbought"
        elif value < 30:
            return "Oversold"
        else:
            return "Neutral"
    
    # Volatility Ratio Momentum Interpretation
    def interpret_vol_momentum(value):
        if pd.isna(value):
            return ""
        elif value > 0.2:
            return "Vol Rising Fast"
        elif value > 0.05:
            return "Vol Rising"
        elif value < -0.2:
            return "Vol Falling Fast"
        elif value < -0.05:
            return "Vol Falling"
        else:
            return "Vol Stable"
    
    # Parkinson vs Standard Ratio Interpretation
    def interpret_park_vs_std(value):
        if pd.isna(value):
            return ""
        elif value > 1.2:
            return "High Intraday Action"
        elif value < 0.8:
            return "Low Intraday Action"
        else:
            return "Normal Intraday"
    
    # Add interpretation columns after their respective feature columns
    
    # Find column positions
    col_positions = {}
    for i, col in enumerate(df.columns):
        col_positions[col] = i
    
    # Add interpretations for existing features
    if 'return_skewness_10d' in df.columns:
        pos = col_positions['return_skewness_10d'] + 1
        df.insert(pos, 'skewness_interpretation', 
                 df['return_skewness_10d'].apply(interpret_skewness))
    
    if 'return_persistence' in df.columns:
        pos = col_positions.get('skewness_interpretation', col_positions['return_persistence']) + 1
        df.insert(pos, 'persistence_interpretation', 
                 df['return_persistence'].apply(interpret_persistence))
    
    if 'volatility_regime' in df.columns:
        pos = col_positions.get('persistence_interpretation', col_positions['volatility_regime']) + 1
        df.insert(pos, 'vol_regime_interpretation', 
                 df['volatility_regime'].apply(interpret_vol_regime))
    
    if 'price_vs_ma20' in df.columns:
        pos = col_positions.get('vol_regime_interpretation', col_positions['price_vs_ma20']) + 1
        df.insert(pos, 'trend_interpretation', 
                 df['price_vs_ma20'].apply(interpret_price_vs_ma))
    
    if 'rsi' in df.columns:
        pos = col_positions.get('trend_interpretation', col_positions['rsi']) + 1
        df.insert(pos, 'rsi_interpretation', 
                 df['rsi'].apply(interpret_rsi))
    
    # Add interpretations for new volatility features
    if 'volatility_momentum' in df.columns:
        pos = col_positions.get('rsi_interpretation', col_positions['volatility_momentum']) + 1
        df.insert(pos, 'vol_momentum_interpretation', 
                 df['volatility_momentum'].apply(interpret_vol_momentum))
    
    if 'parkinson_vs_standard_ratio' in df.columns:
        pos = col_positions.get('vol_momentum_interpretation', col_positions['parkinson_vs_standard_ratio']) + 1
        df.insert(pos, 'park_vs_std_interpretation', 
                 df['parkinson_vs_standard_ratio'].apply(interpret_park_vs_std))
    
    return df
    
    if 'return_persistence' in df.columns:
        pos = col_positions.get('skewness_interpretation', col_positions['return_persistence']) + 1
        df.insert(pos, 'persistence_interpretation', 
                 df['return_persistence'].apply(interpret_persistence))
    
    if 'volatility_regime' in df.columns:
        pos = col_positions.get('persistence_interpretation', col_positions['volatility_regime']) + 1
        df.insert(pos, 'vol_regime_interpretation', 
                 df['volatility_regime'].apply(interpret_vol_regime))
    
    if 'price_vs_ma20' in df.columns:
        pos = col_positions.get('vol_regime_interpretation', col_positions['price_vs_ma20']) + 1
        df.insert(pos, 'trend_interpretation', 
                 df['price_vs_ma20'].apply(interpret_price_vs_ma))
    
    if 'rsi' in df.columns:
        pos = col_positions.get('trend_interpretation', col_positions['rsi']) + 1
        df.insert(pos, 'rsi_interpretation', 
                 df['rsi'].apply(interpret_rsi))
    
    return df

def format_excel_worksheet(worksheet, df):
    """Format the Excel worksheet for better readability"""
    try:
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        from openpyxl.utils import get_column_letter
        
        # Header formatting
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        # Apply header formatting
        for col in range(1, len(df.columns) + 1):
            cell = worksheet.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center")
        
        # Date column formatting - more robust
        if 'Date' in df.columns:
            date_col_idx = df.columns.get_loc('Date') + 1
            date_col_letter = get_column_letter(date_col_idx)
            
            # Format all date cells
            for row in range(2, len(df) + 2):
                cell = worksheet[f"{date_col_letter}{row}"]
                cell.number_format = 'YYYY-MM-DD'
                # Ensure it's treated as a date
                if hasattr(cell.value, 'date'):
                    cell.value = cell.value.date() if hasattr(cell.value, 'date') else cell.value
            
            print(f"✅ Date column formatted: Column {date_col_letter}")
        
        # Set Date column width appropriately
        if 'Date' in df.columns:
            date_col_letter = get_column_letter(df.columns.get_loc('Date') + 1)
            worksheet.column_dimensions[date_col_letter].width = 12
        
        # Price columns formatting (numeric only, no currency symbols)
        price_cols = ['open', 'high', 'low', 'close']
        for col_name in price_cols:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = get_column_letter(col_idx)
                for row in range(2, len(df) + 2):
                    cell = worksheet[f"{col_letter}{row}"]
                    cell.number_format = '#,##0.00'  # Number format without $ symbol
        
        # Volume formatting (whole numbers)
        if 'volume' in df.columns:
            vol_col_idx = df.columns.get_loc('volume') + 1
            vol_col_letter = get_column_letter(vol_col_idx)
            for row in range(2, len(df) + 2):
                cell = worksheet[f"{vol_col_letter}{row}"]
                cell.number_format = '#,##0'  # Whole numbers with commas
        
        # Percentage columns formatting
        pct_cols = [col for col in df.columns if 'return' in col.lower() or 'vs_ma' in col or 'volatility' in col or 'parkinson' in col or 'gk_' in col]
        for col_name in pct_cols:
            if col_name in df.columns and not any(x in col_name.lower() for x in ['interpretation', 'level', 'regime', 'signal', 'trend', 'quality']):
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = get_column_letter(col_idx)
                for row in range(2, len(df) + 2):
                    cell = worksheet[f"{col_letter}{row}"]
                    if 'volatility' in col_name or 'parkinson' in col_name or 'gk_' in col_name:
                        cell.number_format = '0.00'  # Volatility as decimal
                    else:
                        cell.number_format = '0.000'
        
        # Percentile columns formatting (0-100 scale)
        percentile_cols = [col for col in df.columns if 'percentile' in col.lower()]
        for col_name in percentile_cols:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = get_column_letter(col_idx)
                for row in range(2, len(df) + 2):
                    cell = worksheet[f"{col_letter}{row}"]
                    cell.number_format = '0.0'  # One decimal place for percentiles
        
        # Score columns formatting (0-100 scale)
        score_cols = [col for col in df.columns if 'score' in col.lower()]
        for col_name in score_cols:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = get_column_letter(col_idx)
                for row in range(2, len(df) + 2):
                    cell = worksheet[f"{col_letter}{row}"]
                    cell.number_format = '0.0'  # One decimal place for scores
        
        # Flag columns formatting (TRUE/FALSE)
        flag_cols = [col for col in df.columns if 'flag' in col.lower()]
        for col_name in flag_cols:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = get_column_letter(col_idx)
                for row in range(2, len(df) + 2):
                    cell = worksheet[f"{col_letter}{row}"]
                    if cell.value is True:
                        cell.value = "TRUE"
                        cell.fill = PatternFill(start_color="90EE90", end_color="90EE90", fill_type="solid")  # Light green
                    elif cell.value is False:
                        cell.value = "FALSE"
        
        # Days columns formatting (whole numbers)
        days_cols = [col for col in df.columns if 'days_since' in col.lower()]
        for col_name in days_cols:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = get_column_letter(col_idx)
                for row in range(2, len(df) + 2):
                    cell = worksheet[f"{col_letter}{row}"]
                    cell.number_format = '0'  # Whole numbers for days
        
        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 20)  # Max width of 20
            worksheet.column_dimensions[column_letter].width = adjusted_width
        
        # Freeze the header row
        worksheet.freeze_panes = "A2"
        
        print("✅ Excel formatting applied successfully")
        
    except ImportError:
        print("⚠️ openpyxl formatting not available - basic export completed")
    except Exception as e:
        print(f"⚠️ Excel formatting error: {e} - basic export completed")

# Updated download function for single sheet
def download_bitcoin_excel_single(start_date='2020-01-01', end_date=None, filename=None, include_analysis=True):
    """
    Complete workflow: Fetch Bitcoin data, calculate features, save to single Excel sheet
    
    Parameters:
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    filename (str): Output filename (None for auto-generated)
    include_analysis (bool): Whether to include interpretation columns
    
    Returns:
    tuple: (featured_data, filename)
    """
    print("🚀 Starting Bitcoin Data Download & Feature Engineering...")
    print("="*60)
    
    # Step 1: Fetch Bitcoin data
    print("📊 Step 1: Fetching Bitcoin data from Yahoo Finance...")
    btc_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    
    if btc_data is None or btc_data.empty:
        print("❌ Failed to fetch Bitcoin data")
        return None, None
    
    # Step 2: Calculate features
    print("\n🔧 Step 2: Calculating all price-based features...")
    try:
        feature_engine = BitcoinFeatureEngine(btc_data)
        featured_data = feature_engine.calculate_all_features()
        
        # Ensure no timezone issues
        if featured_data.index.tz is not None:
            featured_data.index = featured_data.index.tz_localize(None)
            
    except Exception as e:
        print(f"❌ Error calculating features: {e}")
        return None, None
    
    # Step 3: Save to single Excel sheet
    print(f"\n💾 Step 3: Saving to single Excel sheet...")
    saved_filename = save_to_excel_single_sheet(featured_data, filename=filename, include_analysis=include_analysis)
    
    if saved_filename:
        print(f"\n✅ COMPLETE! Bitcoin data saved to: {saved_filename}")
        
        # Show quick summary
        print(f"\n📋 QUICK SUMMARY:")
        print(f"📅 Data Period: {featured_data.index.min().date()} to {featured_data.index.max().date()}")
        print(f"📊 Total Records: {len(featured_data):,}")
        print(f"🔢 Total Features: {len(featured_data.columns)}")
        print(f"💰 Current Price: ${featured_data['close'].iloc[-1]:,.2f}")
        print(f"📈 30-day Return: {((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%")
        
        return featured_data, saved_filename
    else:
        return featured_data, None

# Updated quick download functions for single sheet
def download_bitcoin_1year_single(filename=None):
    """Download last 1 year of Bitcoin data to single sheet"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=1)
    return download_bitcoin_excel_single(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_1year_single.xlsx'
    )

def download_bitcoin_2years_single(filename=None):
    """Download last 2 years of Bitcoin data to single sheet"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=2)
    return download_bitcoin_excel_single(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_2years_single.xlsx'
    )

def download_bitcoin_5years_single(filename=None):
    """Download last 5 years of Bitcoin data to single sheet"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=5)
    return download_bitcoin_excel_single(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_5years_single.xlsx'
    )

def download_bitcoin_max_single(filename=None):
    """Download maximum available Bitcoin data to single sheet"""
    return download_bitcoin_excel_single(
        start_date='2010-01-01',
        filename=filename or 'bitcoin_max_data_single.xlsx'
    )

def create_excel_summary(featured_data):
    """Create summary statistics for Excel export"""
    latest = featured_data.iloc[-1]
    
    summary = {
        'Metric': [
            'Current Price',
            'Daily Return (%)',
            '30-day Return (%)',
            'YTD Return (%)',
            '20-day Volatility (%)',
            'Return Skewness (10d)',
            'Return Persistence',
            'Price vs MA20 (%)',
            'Price vs MA50 (%)',
            'Volatility Regime',
            'Trend Strength',
            'RSI',
            '30-day Price Percentile',
            'Z-Score (20d)'
        ],
        'Value': [
            f"${latest['close']:,.2f}",
            f"{latest['log_return_1d']*100:.2f}%",
            f"{((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%",
            f"{((featured_data['close'].iloc[-1] / featured_data['close'].iloc[0]) - 1) * 100:.2f}%",
            f"{latest['volatility_20d']:.1f}%",
            f"{latest['return_skewness_10d']:.3f}",
            f"{latest['return_persistence']:.3f}",
            f"{latest['price_vs_ma20']:.2f}%",
            f"{latest['price_vs_ma50']:.2f}%",
            f"{latest['volatility_regime']:.0f}",
            f"{latest['trend_strength']:.3f}",
            f"{latest['rsi']:.1f}",
            f"{latest['price_percentile_30d']:.1f}%",
            f"{latest['z_score_20d']:.3f}"
        ],
        'Interpretation': [
            'Current Bitcoin price',
            'Today\'s price change',
            'Monthly performance',
            'Year-to-date performance',
            'Risk/uncertainty level',
            'Asymmetry in returns',
            'Directional consistency',
            'Short-term trend position',
            'Medium-term trend position',
            '0=Low, 1=Med, 2=High vol',
            'Trend consistency (0-1)',
            'Momentum indicator (0-100)',
            'Relative price position',
            'Distance from mean'
        ]
    }
    
    return pd.DataFrame(summary)

def get_tier_features(featured_data, tier=1):
    """Get specific tier features for Excel export"""
    if tier == 1:
        features = ['log_return_1d', 'volatility_20d', 'price_vs_ma20', 'return_momentum_5d', 'daily_range']
    elif tier == 2:
        features = ['volatility_ratio', 'price_vs_ma50', 'ma20_vs_ma50', 'price_percentile_30d', 'return_acceleration']
    elif tier == 3:
        features = ['z_score_20d', 'trend_persistence', 'volatility_momentum', 'return_skewness_10d', 'return_persistence']
    else:
        return pd.DataFrame()
    
    available_features = [f for f in features if f in featured_data.columns]
    return featured_data[available_features]

def create_market_analysis(featured_data):
    """Create market analysis summary for Excel"""
    latest = featured_data.iloc[-1]
    
    # Market condition interpretations
    analysis = []
    
    # Price trend analysis
    if latest['price_vs_ma20'] > 5:
        trend_analysis = "Strong uptrend - Price well above 20-day MA"
    elif latest['price_vs_ma20'] > 0:
        trend_analysis = "Mild uptrend - Price above 20-day MA"
    elif latest['price_vs_ma20'] > -5:
        trend_analysis = "Mild downtrend - Price below 20-day MA"
    else:
        trend_analysis = "Strong downtrend - Price well below 20-day MA"
    
    # Volatility analysis
    if latest['volatility_regime'] == 2:
        vol_analysis = "High volatility environment - Expect large price swings"
    elif latest['volatility_regime'] == 1:
        vol_analysis = "Medium volatility - Normal market conditions"
    else:
        vol_analysis = "Low volatility - Calm market conditions"
    
    # Momentum analysis
    if latest['return_persistence'] > 0.8:
        momentum_analysis = "Strong momentum - Trend likely to continue"
    elif latest['return_persistence'] > 0.6:
        momentum_analysis = "Moderate momentum - Some directional bias"
    else:
        momentum_analysis = "Weak momentum - Choppy/sideways movement"
    
    # Risk analysis
    if latest['return_skewness_10d'] < -0.5:
        risk_analysis = "Elevated crash risk - Recent negative skew in returns"
    elif latest['return_skewness_10d'] > 0.5:
        risk_analysis = "Upside bias - Recent positive skew in returns"
    else:
        risk_analysis = "Balanced risk - Symmetric return distribution"
    
    analysis = [
        {'Category': 'Price Trend', 'Analysis': trend_analysis, 'Value': f"{latest['price_vs_ma20']:.2f}%"},
        {'Category': 'Volatility', 'Analysis': vol_analysis, 'Value': f"{latest['volatility_20d']:.1f}%"},
        {'Category': 'Momentum', 'Analysis': momentum_analysis, 'Value': f"{latest['return_persistence']:.3f}"},
        {'Category': 'Risk Profile', 'Analysis': risk_analysis, 'Value': f"{latest['return_skewness_10d']:.3f}"},
    ]
    
    return pd.DataFrame(analysis)

# Complete workflow function
def download_bitcoin_excel(start_date='2020-01-01', end_date=None, filename=None):
    """
    Complete workflow: Fetch Bitcoin data, calculate features, save to Excel
    
    Parameters:
    start_date (str): Start date for data fetching
    end_date (str): End date for data fetching (None for latest)
    filename (str): Output filename (None for auto-generated)
    
    Returns:
    tuple: (featured_data, filename)
    """
    print("🚀 Starting Bitcoin Data Download & Feature Engineering...")
    print("="*60)
    
    # Step 1: Fetch Bitcoin data
    print("📊 Step 1: Fetching Bitcoin data from Yahoo Finance...")
    btc_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    
    if btc_data is None or btc_data.empty:
        print("❌ Failed to fetch Bitcoin data")
        return None, None
    
    # Step 2: Calculate features
    print("\n🔧 Step 2: Calculating all price-based features...")
    try:
        feature_engine = BitcoinFeatureEngine(btc_data)
        featured_data = feature_engine.calculate_all_features()
        
        # Ensure no timezone issues
        if featured_data.index.tz is not None:
            featured_data.index = featured_data.index.tz_localize(None)
            
    except Exception as e:
        print(f"❌ Error calculating features: {e}")
        return None, None
    
    # Step 3: Save to Excel
    print(f"\n💾 Step 3: Saving to Excel...")
    saved_filename = save_to_excel(featured_data, filename=filename, include_summary=True)
    
    if saved_filename:
        print(f"\n✅ COMPLETE! Bitcoin data saved to: {saved_filename}")
        
        # Show quick summary
        print(f"\n📋 QUICK SUMMARY:")
        print(f"📅 Data Period: {featured_data.index.min().date()} to {featured_data.index.max().date()}")
        print(f"📊 Total Records: {len(featured_data):,}")
        print(f"🔢 Total Features: {len(featured_data.columns)}")
        print(f"💰 Current Price: ${featured_data['close'].iloc[-1]:,.2f}")
        print(f"📈 30-day Return: {((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%")
        
        return featured_data, saved_filename
    else:
        return featured_data, None

# Quick download functions for different periods
def download_bitcoin_1year(filename=None):
    """Download last 1 year of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=1)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_1year.xlsx'
    )

def download_bitcoin_2years(filename=None):
    """Download last 2 years of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=2)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_2years.xlsx'
    )

def download_bitcoin_5years(filename=None):
    """Download last 5 years of Bitcoin data"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=5)
    return download_bitcoin_excel(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_5years.xlsx'
    )

def download_bitcoin_max(filename=None):
    """Download maximum available Bitcoin data"""
    return download_bitcoin_excel(
        start_date='2010-01-01',
        filename=filename or 'bitcoin_max_data.xlsx'
    )

# Quick test function to verify date handling
def test_bitcoin_dates():
    """Quick test to verify date handling is working correctly"""
    print("🧪 TESTING: Bitcoin Date Handling")
    print("="*40)
    
    # Fetch small amount of data for testing
    test_data = fetch_bitcoin_data(start_date='2024-05-01', end_date='2024-05-10', debug=True)
    
    if test_data is not None:
        print(f"\n📊 Test data shape: {test_data.shape}")
        print(f"📅 Index type: {type(test_data.index)}")
        print(f"📅 Index dtype: {test_data.index.dtype}")
        
        # Test the Excel export with small dataset
        filename = 'test_bitcoin_dates.xlsx'
        export_data = test_data.copy()
        export_data = export_data.reset_index()
        
        if 'index' in export_data.columns:
            export_data.rename(columns={'index': 'Date'}, inplace=True)
        
        print(f"\n📋 Export data columns: {list(export_data.columns)}")
        print(f"📅 Date column type: {export_data['Date'].dtype}")
        print(f"\nFirst 3 dates in export:")
        for i, date in enumerate(export_data['Date'].head(3)):
            print(f"  {i+1}. {date} (type: {type(date)})")
        
        # Try saving to Excel
        try:
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                export_data.to_excel(writer, sheet_name='Test_Dates', index=False)
            print(f"\n✅ Test Excel file saved: {filename}")
            return True
        except Exception as e:
            print(f"\n❌ Test Excel save failed: {e}")
            return False
    
    return False

if __name__ == "__main__":
    # Example usage - Download Bitcoin data to single Excel sheet
    print("🚀 Bitcoin Feature Engineering & Single Sheet Excel Export")
    print("=" * 60)
    
    # Uncomment this line to test date handling first
    # test_bitcoin_dates()
    
    # Option 1: Download last 2 years to single sheet with analysis
    print("\n📊 Downloading last 2 years of Bitcoin data to single sheet...")
    data, filename = download_bitcoin_2years_single()
    
    # Option 2: Custom date range to single sheet
    # data, filename = download_bitcoin_excel_single(
    #     start_date='2023-01-01', 
    #     end_date='2024-12-31',
    #     filename='bitcoin_2023_2024_single.xlsx'
    # )
    
    # Option 3: Maximum data available to single sheet
    # data, filename = download_bitcoin_max_single()
    
    # Option 4: Without interpretation columns (just raw data + features)
    # data, filename = download_bitcoin_excel_single(
    #     start_date='2022-01-01',
    #     filename='bitcoin_raw_features.xlsx',
    #     include_analysis=False
    # )
    
    if filename:
        print(f"\n🎉 Success! Check your file: {filename}")
        print(f"\n📊 File contains:")
        print(f"  • Date column (YYYY-MM-DD format)")
        print(f"  • OHLCV price data")
        print(f"  • 45+ technical features")
        print(f"  • Enhanced volatility analysis:")
        print(f"    - Parkinson volatility interpretations")
        print(f"    - Garman-Klass volatility analysis") 
        print(f"    - Volatility ratio enhancements")
        print(f"    - Volatility percentile rankings")
        print(f"    - Volatility high/low analysis")
        print(f"    - Advanced volatility indicators")
        print(f"    - Composite volatility scores")
        print(f"  • Interpretation columns for key features")
        print(f"  • Professional Excel formatting")
        print(f"\n🔍 New Volatility Features Added:")
        print(f"  • 25+ volatility interpretation columns")
        print(f"  • Regime classification systems")
        print(f"  • Risk/opportunity scoring")
        print(f"  • Percentile-based analysis")
    else:
        print("\n❌ Download failed!")

import pandas as pd
import numpy as np
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

class EnhancedBitcoinFeatureEngine:
    """
    Comprehensive feature engineering for Bitcoin price prediction and signal generation
    Includes all volatility models features plus trading-specific features
    """
    
    def __init__(self, df):
        """
        Initialize with OHLCV dataframe
        Expected columns: ['open', 'high', 'low', 'close', 'volume']
        Index should be datetime
        """
        self.df = df.copy()
        self.df.columns = self.df.columns.str.lower()
        required_cols = ['open', 'high', 'low', 'close']
        
        if not all(col in self.df.columns for col in required_cols):
            raise ValueError(f"DataFrame must contain columns: {required_cols}")
    
    def calculate_all_features(self):
        """Calculate comprehensive feature set for all models"""
        print("🔧 Calculating Enhanced Bitcoin Features...")
        
        # 1. Basic Price Features
        self._calculate_price_features()
        
        # 2. Returns Features
        self._calculate_returns()
        
        # 3. Enhanced Volatility Features  
        self._calculate_volatility()
        
        # 4. Technical Indicators
        self._calculate_technical_indicators()
        
        # 5. Moving Average Features
        self._calculate_ma_features()
        
        # 6. Range Features
        self._calculate_range_features()
        
        # 7. Position and Momentum Features
        self._calculate_position_momentum()
        
        # 8. Volume and Microstructure
        self._calculate_volume_features()
        
        # 9. Support and Resistance
        self._calculate_support_resistance()
        
        # 10. Market Regime Features
        self._calculate_market_regime()
        
        # 11. Time-based Features
        self._calculate_time_features()
        
        # 12. Advanced Features
        self._calculate_advanced_features()
        
        # 13. Target Variables
        self._calculate_target_variables()
        
        # 14. Model-Specific Feature Sets
        self._create_model_feature_sets()
        
        return self.df
    
    def _calculate_price_features(self):
        """Calculate basic price-based features"""
        print("  - Price features...")
        
        close = self.df['close']
        
        # Basic transformations
        self.df['log_close'] = np.log(close)
        self.df['price_change'] = close.diff()
        self.df['price_pct_change'] = close.pct_change()
        
        # Price normalization
        self.df['price_normalized'] = (close - close.rolling(252).mean()) / close.rolling(252).std()
        self.df['price_position_annual'] = close.rolling(252).rank(pct=True) * 100
        
        # Distance from extremes
        self.df['price_vs_high_52w'] = (close.rolling(252).max() - close) / close * 100
        self.df['price_vs_low_52w'] = (close - close.rolling(252).min()) / close * 100
        
        # Multi-timeframe momentum
        for period in [1, 3, 5, 10, 20]:
            self.df[f'price_momentum_{period}d'] = close.pct_change(period) * 100
    
    def _calculate_returns(self):
        """Calculate return-based features"""
        print("  - Returns features...")
        
        # Basic returns
        self.df['simple_return_1d'] = self.df['close'].pct_change()
        self.df['log_return_1d'] = np.log(self.df['close'] / self.df['close'].shift(1))
        
        # Multi-period returns
        for period in [3, 7, 14, 30]:
            self.df[f'log_return_{period}d'] = self.df['log_return_1d'].rolling(period).sum()
            self.df[f'simple_return_{period}d'] = self.df['close'].pct_change(period)
        
        # Return derivatives
        self.df['return_momentum_5d'] = self.df['log_return_1d'].rolling(5).mean()
        self.df['return_acceleration'] = self.df['log_return_1d'].diff()
        
        # Return volatility ratio
        rolling_vol = self.df['log_return_1d'].rolling(20).std()
        self.df['return_volatility_ratio'] = self.df['log_return_1d'] / rolling_vol
        
        # Return skewness and kurtosis
        self.df['return_skewness_10d'] = self.df['log_return_1d'].rolling(10).apply(
            lambda x: stats.skew(x.dropna()) if len(x.dropna()) >= 3 else np.nan
        )
        self.df['return_kurtosis_10d'] = self.df['log_return_1d'].rolling(10).apply(
            lambda x: stats.kurtosis(x.dropna()) if len(x.dropna()) >= 3 else np.nan
        )
        
        # Return persistence
        self.df['return_persistence'] = self._calculate_return_persistence()
        
        # Risk-adjusted returns
        self.df['sharpe_10d'] = (self.df['return_momentum_5d'] * np.sqrt(252)) / (rolling_vol * np.sqrt(252))
        
        # Return autocorrelation
        self.df['return_autocorr_5d'] = self.df['log_return_1d'].rolling(10).apply(
            lambda x: x.autocorr(lag=1) if len(x.dropna()) > 5 else np.nan
        )
        
        # Asymmetry features for volatility models
        self.df['negative_return_flag'] = (self.df['log_return_1d'] < 0).astype(int)
        self.df['positive_return_flag'] = (self.df['log_return_1d'] > 0).astype(int)
        self.df['abs_return'] = abs(self.df['log_return_1d'])
        self.df['return_magnitude'] = self.df['abs_return']
        self.df['negative_squared_return'] = self.df['log_return_1d']**2 * self.df['negative_return_flag']
        self.df['positive_squared_return'] = self.df['log_return_1d']**2 * self.df['positive_return_flag']
        self.df['leverage_proxy'] = self.df['log_return_1d'] * self.df['negative_return_flag']
    
    def _calculate_return_persistence(self):
        """Calculate return persistence - consistency of return direction"""
        returns = self.df['log_return_1d']
        return_signs = np.sign(returns)
        consistency = return_signs.rolling(5).apply(
            lambda x: len(x[x == x.iloc[-1]]) / len(x) if len(x) > 0 else np.nan
        )
        return consistency
    
    def _calculate_volatility(self):
        """Calculate comprehensive volatility features"""
        print("  - Enhanced volatility features...")
        
        returns = self.df['log_return_1d']
        
        # Historical volatility (multiple windows)
        for window in [5, 10, 20, 30, 60]:
            self.df[f'volatility_{window}d'] = returns.rolling(window).std() * np.sqrt(252)
        
        # Parkinson volatility
        try:
            self.df['parkinson_vol'] = np.sqrt(
                np.log(self.df['high'] / self.df['low']).rolling(20).apply(
                    lambda x: (x**2).mean() / (4 * np.log(2)) if len(x) > 0 else np.nan
                )
            ) * np.sqrt(252)
        except:
            self.df['parkinson_vol'] = self.df['volatility_20d']
        
        # Garman-Klass volatility
        try:
            self.df['garman_klass_vol'] = self._calculate_garman_klass()
        except:
            self.df['garman_klass_vol'] = self.df['volatility_20d']
        
        # Volatility relationships
        self.df['volatility_ratio'] = self.df['volatility_5d'] / self.df['volatility_60d']
        self.df['volatility_momentum'] = self.df['volatility_20d'].pct_change(5)
        
        # Volatility regime classification
        vol_20d = self.df['volatility_20d']
        try:
            vol_low = vol_20d.rolling(252).quantile(0.33)
            vol_high = vol_20d.rolling(252).quantile(0.67)
            conditions = [
                vol_20d <= vol_low,
                (vol_20d > vol_low) & (vol_20d <= vol_high),
                vol_20d > vol_high
            ]
            self.df['volatility_regime'] = np.select(conditions, [0, 1, 2], default=np.nan)
        except:
            vol_median = vol_20d.rolling(252).median()
            self.df['volatility_regime'] = np.where(vol_20d > vol_median, 1, 0)
        
        # Volatility mean reversion
        vol_mean = self.df['volatility_20d'].rolling(252).mean()
        self.df['volatility_mean_reversion'] = (self.df['volatility_20d'] - vol_mean) / vol_mean
        
        # Advanced volatility features
        try:
            self.df['volatility_clustering'] = self._calculate_vol_clustering()
        except:
            self.df['volatility_clustering'] = self.df['volatility_20d'].rolling(10).std()
            
        self.df['vol_of_vol'] = self.df['volatility_20d'].rolling(20).std()
        
        # Enhanced volatility interpretations
        self._add_volatility_interpretations()
    
    def _calculate_technical_indicators(self):
        """Calculate technical indicators"""
        print("  - Technical indicators...")
        
        close = self.df['close']
        high = self.df['high']
        low = self.df['low']
        volume = self.df['volume']
        
        # RSI
        delta = close.diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        for period in [14, 30]:
            avg_gain = gain.rolling(period).mean()
            avg_loss = loss.rolling(period).mean()
            rs = avg_gain / avg_loss
            self.df[f'rsi_{period}'] = 100 - (100 / (1 + rs))
        
        # MACD
        ema12 = close.ewm(span=12).mean()
        ema26 = close.ewm(span=26).mean()
        self.df['macd'] = ema12 - ema26
        self.df['macd_signal'] = self.df['macd'].ewm(span=9).mean()
        self.df['macd_histogram'] = self.df['macd'] - self.df['macd_signal']
        
        # Stochastic
        lowest_low = low.rolling(14).min()
        highest_high = high.rolling(14).max()
        self.df['stochastic_k'] = 100 * (close - lowest_low) / (highest_high - lowest_low)
        self.df['stochastic_d'] = self.df['stochastic_k'].rolling(3).mean()
        
        # Bollinger Bands
        bb_middle = close.rolling(20).mean()
        bb_std = close.rolling(20).std()
        self.df['bollinger_upper'] = bb_middle + (2 * bb_std)
        self.df['bollinger_lower'] = bb_middle - (2 * bb_std)
        self.df['bollinger_position'] = (close - self.df['bollinger_lower']) / (self.df['bollinger_upper'] - self.df['bollinger_lower'])
        self.df['bollinger_width'] = (self.df['bollinger_upper'] - self.df['bollinger_lower']) / bb_middle
        
        # Williams %R
        self.df['williams_r'] = -100 * (highest_high - close) / (highest_high - lowest_low)
        
        # Commodity Channel Index (CCI)
        typical_price = (high + low + close) / 3
        sma_tp = typical_price.rolling(20).mean()
        mad = typical_price.rolling(20).apply(lambda x: np.mean(np.abs(x - x.mean())))
        self.df['cci'] = (typical_price - sma_tp) / (0.015 * mad)
        
        # Rate of Change (ROC)
        for period in [10, 20]:
            self.df[f'roc_{period}'] = ((close - close.shift(period)) / close.shift(period)) * 100
    
    def _calculate_ma_features(self):
        """Calculate moving average features"""
        print("  - Moving average features...")
        
        close = self.df['close']
        
        # Multiple moving averages
        ma_periods = [5, 10, 20, 50, 100, 200]
        for period in ma_periods:
            self.df[f'ma{period}'] = close.rolling(period).mean()
            self.df[f'price_vs_ma{period}'] = (close - self.df[f'ma{period}']) / self.df[f'ma{period}'] * 100
        
        # EMA (Exponential Moving Average)
        for period in [12, 26, 50]:
            self.df[f'ema{period}'] = close.ewm(span=period).mean()
            self.df[f'price_vs_ema{period}'] = (close - self.df[f'ema{period}']) / self.df[f'ema{period}'] * 100
        
        # MA relationships
        self.df['ma5_vs_ma20'] = (self.df['ma5'] - self.df['ma20']) / self.df['ma20'] * 100
        self.df['ma20_vs_ma50'] = (self.df['ma20'] - self.df['ma50']) / self.df['ma50'] * 100
        self.df['ma50_vs_ma200'] = (self.df['ma50'] - self.df['ma200']) / self.df['ma200'] * 100
        
        # MA convergence and divergence
        ma_spread = np.abs(self.df['ma20'] - self.df['ma50']) + np.abs(self.df['ma50'] - self.df['ma200'])
        self.df['ma_convergence'] = ma_spread / close
        
        # MA slopes (momentum)
        for period in [20, 50]:
            self.df[f'ma{period}_slope'] = self.df[f'ma{period}'].pct_change(5) * 100
        
        self.df['ma_acceleration'] = self.df['ma20_slope'].diff()
        
        # Binary position features
        self.df['price_above_ma20'] = (close > self.df['ma20']).astype(int)
        self.df['price_above_ma50'] = (close > self.df['ma50']).astype(int)
        self.df['price_above_ma200'] = (close > self.df['ma200']).astype(int)
        self.df['price_above_all_mas'] = (
            (close > self.df['ma20']) & 
            (close > self.df['ma50']) & 
            (close > self.df['ma200'])
        ).astype(int)
        
        # Golden/Death cross indicators
        self.df['golden_cross'] = ((self.df['ma50'] > self.df['ma200']) & 
                                  (self.df['ma50'].shift(1) <= self.df['ma200'].shift(1))).astype(int)
        self.df['death_cross'] = ((self.df['ma50'] < self.df['ma200']) & 
                                 (self.df['ma50'].shift(1) >= self.df['ma200'].shift(1))).astype(int)
        
        # Bullish/Bearish alignment
        self.df['ma_bullish_alignment'] = (
            (self.df['ma20'] > self.df['ma50']) & 
            (self.df['ma50'] > self.df['ma200'])
        ).astype(int)
        self.df['ma_bearish_alignment'] = (
            (self.df['ma20'] < self.df['ma50']) & 
            (self.df['ma50'] < self.df['ma200'])
        ).astype(int)
    
    def _calculate_range_features(self):
        """Calculate high-low range features"""
        print("  - Range features...")
        
        o, h, l, c = self.df['open'], self.df['high'], self.df['low'], self.df['close']
        prev_close = c.shift(1)
        
        # Daily range indicators
        self.df['daily_range'] = (h - l) / c * 100
        avg_range = self.df['daily_range'].rolling(20).mean()
        self.df['range_vs_average'] = self.df['daily_range'] / avg_range
        self.df['range_position'] = (c - l) / (h - l)
        self.df['gap_open'] = (o - prev_close) / prev_close * 100
        
        # True Range and ATR
        tr1 = h - l
        tr2 = np.abs(h - prev_close)
        tr3 = np.abs(l - prev_close)
        self.df['true_range'] = np.maximum(tr1, np.maximum(tr2, tr3))
        
        for period in [14, 21, 30]:
            self.df[f'average_true_range_{period}'] = self.df['true_range'].rolling(period).mean()
        
        # Range expansion/contraction
        range_ma = self.df['daily_range'].rolling(10).mean()
        self.df['range_expansion'] = self.df['daily_range'] / range_ma
        self.df['range_contraction'] = 1 / self.df['range_expansion']
        
        # High-low spreads
        self.df['high_low_spread'] = (h - l) / c
        self.df['close_vs_high'] = (h - c) / c * 100
        self.df['close_vs_low'] = (c - l) / c * 100
    
    def _calculate_position_momentum(self):
        """Calculate price position and momentum features"""
        print("  - Position and momentum features...")
        
        close = self.df['close']
        
        # Price percentiles (multiple timeframes)
        for period in [10, 20, 30, 60, 90, 252]:
            self.df[f'price_percentile_{period}d'] = close.rolling(period).rank(pct=True) * 100
        
        # Distance from highs/lows
        for period in [20, 30, 60, 252]:
            rolling_high = close.rolling(period).max()
            rolling_low = close.rolling(period).min()
            self.df[f'distance_from_high_{period}d'] = (rolling_high - close) / close * 100
            self.df[f'distance_from_low_{period}d'] = (close - rolling_low) / close * 100
        
        # Momentum features (multiple timeframes)
        for period in [5, 10, 20]:
            self.df[f'momentum_{period}d'] = close.pct_change(period) * 100
        
        # Momentum persistence
        for period in [5, 10]:
            momentum_sign = np.sign(self.df[f'momentum_{period}d'])
            self.df[f'momentum_persistence_{period}d'] = momentum_sign.rolling(5).apply(
                lambda x: (x == x.iloc[-1]).sum() / len(x) if len(x) > 0 else np.nan
            )
        
        # Relative strength vs SPY (if available, else vs its own history)
        self.df['relative_strength'] = close / close.rolling(252).mean()
    
    def _calculate_volume_features(self):
        """Calculate volume and microstructure features"""
        print("  - Volume and microstructure features...")
        
        volume = self.df['volume']
        close = self.df['close']
        high = self.df['high']
        low = self.df['low']
        
        # Volume moving averages
        for period in [10, 20, 50]:
            self.df[f'volume_sma_{period}'] = volume.rolling(period).mean()
            self.df[f'volume_ratio_{period}'] = volume / self.df[f'volume_sma_{period}']
        
        # Volume momentum
        for period in [5, 10]:
            self.df[f'volume_momentum_{period}d'] = volume.pct_change(period)
        
        # Price-volume relationship
        self.df['price_volume_trend'] = (close.pct_change() * volume).rolling(10).mean()
        self.df['volume_price_correlation'] = close.rolling(20).corr(volume)
        
        # On-Balance Volume (OBV)
        price_change = close.diff()
        obv = np.where(price_change > 0, volume, 
                      np.where(price_change < 0, -volume, 0))
        self.df['on_balance_volume'] = np.cumsum(obv)
        self.df['obv_momentum'] = self.df['on_balance_volume'].pct_change(10)
        
        # Volume Rate of Change
        for period in [10, 20]:
            self.df[f'volume_roc_{period}'] = volume.pct_change(period) * 100
        
        # Accumulation/Distribution Line
        clv = ((close - low) - (high - close)) / (high - low)
        self.df['accumulation_distribution'] = (clv * volume).cumsum()
        
        # Money Flow Index
        typical_price = (high + low + close) / 3
        money_flow = typical_price * volume
        
        positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0).rolling(14).sum()
        negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0).rolling(14).sum()
        money_ratio = positive_flow / negative_flow
        self.df['money_flow_index'] = 100 - (100 / (1 + money_ratio))
        
        # Volume-Weighted Average Price (VWAP) - simplified daily
        vwap_numerator = (typical_price * volume).rolling(20).sum()
        vwap_denominator = volume.rolling(20).sum()
        self.df['vwap_20'] = vwap_numerator / vwap_denominator
        self.df['price_vs_vwap'] = (close - self.df['vwap_20']) / self.df['vwap_20'] * 100
    
    def _calculate_support_resistance(self):
        """Calculate support and resistance levels"""
        print("  - Support and resistance features...")
        
        close = self.df['close']
        high = self.df['high']
        low = self.df['low']
        
        # Pivot points
        prev_high = high.shift(1)
        prev_low = low.shift(1)
        prev_close = close.shift(1)
        
        self.df['pivot_point'] = (prev_high + prev_low + prev_close) / 3
        self.df['pivot_r1'] = 2 * self.df['pivot_point'] - prev_low
        self.df['pivot_s1'] = 2 * self.df['pivot_point'] - prev_high
        self.df['pivot_r2'] = self.df['pivot_point'] + (prev_high - prev_low)
        self.df['pivot_s2'] = self.df['pivot_point'] - (prev_high - prev_low)
        
        # Distance to pivot levels
        self.df['distance_to_pivot'] = abs(close - self.df['pivot_point']) / close * 100
        self.df['distance_to_r1'] = abs(close - self.df['pivot_r1']) / close * 100
        self.df['distance_to_s1'] = abs(close - self.df['pivot_s1']) / close * 100
        
        # Support/Resistance strength (simplified)
        for period in [20, 50]:
            rolling_max = high.rolling(period).max()
            rolling_min = low.rolling(period).min()
            
            self.df[f'resistance_strength_{period}'] = (rolling_max - close) / close * 100
            self.df[f'support_strength_{period}'] = (close - rolling_min) / close * 100
        
        # Breakout detection
        self.df['breakout_up_20'] = (close > high.rolling(20).max().shift(1)).astype(int)
        self.df['breakout_down_20'] = (close < low.rolling(20).min().shift(1)).astype(int)
    
    def _calculate_market_regime(self):
        """Calculate market regime features"""
        print("  - Market regime features...")
        
        close = self.df['close']
        vol_20d = self.df['volatility_20d']
        
        # Volatility regimes (enhanced)
        vol_low = vol_20d.rolling(252).quantile(0.25)
        vol_high = vol_20d.rolling(252).quantile(0.75)
        
        self.df['vol_regime_low'] = (vol_20d <= vol_low).astype(int)
        self.df['vol_regime_medium'] = ((vol_20d > vol_low) & (vol_20d <= vol_high)).astype(int)
        self.df['vol_regime_high'] = (vol_20d > vol_high).astype(int)
        
        # Trend regimes
        ma200 = self.df['ma200']
        price_above_ma200 = close > ma200
        ma200_slope = ma200.pct_change(20)
        
        self.df['bull_market_flag'] = (price_above_ma200 & (ma200_slope > 0)).astype(int)
        self.df['bear_market_flag'] = (~price_above_ma200 & (ma200_slope < 0)).astype(int)
        self.df['sideways_market_flag'] = (~self.df['bull_market_flag'] & ~self.df['bear_market_flag']).astype(int)
        
        # Trend strength
        self.df['trend_regime_strength'] = abs(self.df['price_vs_ma200'])
        
        # Market stress indicators
        self.df['market_stress_indicator'] = (
            (vol_20d > vol_20d.rolling(252).quantile(0.8)) & 
            (self.df['return_skewness_10d'] < -0.5)
        ).astype(int)
        
        # Risk-on/Risk-off indicators
        risk_on_conditions = [
            self.df['price_vs_ma20'] > 0,
            self.df['rsi_14'] > 50,
            vol_20d < vol_20d.rolling(60).median()
        ]
        self.df['risk_on_score'] = sum(risk_on_conditions)
        
        # Regime transitions
        self.df['vol_regime_transition'] = (self.df['vol_regime_low'].diff() != 0).astype(int)
        self.df['trend_regime_transition'] = (self.df['bull_market_flag'].diff() != 0).astype(int)
    
    def _calculate_time_features(self):
        """Calculate time-based features"""
        print("  - Time-based features...")
        
        # Basic time features
        self.df['day_of_week'] = self.df.index.dayofweek
        self.df['month'] = self.df.index.month
        self.df['quarter'] = self.df.index.quarter
        self.df['year'] = self.df.index.year
        
        # End-of-period effects
        self.df['is_month_end'] = (self.df.index + pd.DateOffset(days=1)).month != self.df.index.month
        self.df['is_quarter_end'] = (self.df.index + pd.DateOffset(days=1)).quarter != self.df.index.quarter
        self.df['is_year_end'] = (self.df.index + pd.DateOffset(days=1)).year != self.df.index.year
        
        # Days since events
        self.df['days_since_month_start'] = self.df.index.day
        self.df['days_since_quarter_start'] = (self.df.index - self.df.index.to_period('Q').start_time).days
        
        # Cyclical encoding for ML
        self.df['day_of_week_sin'] = np.sin(2 * np.pi * self.df['day_of_week'] / 7)
        self.df['day_of_week_cos'] = np.cos(2 * np.pi * self.df['day_of_week'] / 7)
        self.df['month_sin'] = np.sin(2 * np.pi * self.df['month'] / 12)
        self.df['month_cos'] = np.cos(2 * np.pi * self.df['month'] / 12)
        
        # Weekend effect
        self.df['is_weekend'] = (self.df['day_of_week'] >= 5).astype(int)
        self.df['is_monday'] = (self.df['day_of_week'] == 0).astype(int)
        self.df['is_friday'] = (self.df['day_of_week'] == 4).astype(int)
    
    def _calculate_advanced_features(self):
        """Calculate advanced features"""
        print("  - Advanced features...")
        
        close = self.df['close']
        returns = self.df['log_return_1d']
        
        # Z-scores (mean reversion indicators)
        for period in [20, 60, 252]:
            rolling_mean = close.rolling(period).mean()
            rolling_std = close.rolling(period).std()
            self.df[f'z_score_{period}d'] = (close - rolling_mean) / rolling_std
        
        # Mean reversion pressure
        long_term_mean = close.rolling(252).mean()
        self.df['mean_reversion_pressure'] = (close - long_term_mean) / long_term_mean * 100
        
        # Trend quality features
        self.df['trend_strength'] = self._calculate_trend_strength()
        self.df['trend_persistence'] = self._calculate_trend_persistence()
        self.df['trend_quality_score'] = self._calculate_trend_quality()
        
        # Market microstructure approximations
        self.df['noise_ratio'] = self._calculate_noise_ratio()
        self.df['market_efficiency'] = self._calculate_market_efficiency()
        
        # Hurst exponent (trend vs mean reversion tendency)
        self.df['hurst_exponent'] = returns.rolling(50).apply(
            lambda x: self._calculate_hurst(x.dropna()) if len(x.dropna()) > 10 else np.nan
        )
        
        # Fractal dimension
        self.df['fractal_dimension'] = 2 - self.df['hurst_exponent']
        
        # Information ratio
        self.df['information_ratio'] = returns.rolling(60).mean() / returns.rolling(60).std()
        
        # Maximum drawdown
        self.df['max_drawdown'] = self._calculate_max_drawdown(close)
        
        # Calmar ratio
        annual_return = returns.rolling(252).sum()
        self.df['calmar_ratio'] = annual_return / abs(self.df['max_drawdown'])
    
    def _calculate_target_variables(self):
        """Calculate target variables for prediction"""
        print("  - Target variables...")
        
        close = self.df['close']
        returns = self.df['log_return_1d']
        
        # Future prices (for regression models)
        for period in [1, 3, 5, 10]:
            self.df[f'close_next_{period}d'] = close.shift(-period)
            self.df[f'high_next_{period}d'] = self.df['high'].shift(-period)
            self.df[f'low_next_{period}d'] = self.df['low'].shift(-period)
        
        # Future returns (for regression models)
        for period in [1, 3, 5, 10]:
            self.df[f'return_next_{period}d'] = close.pct_change(period).shift(-period)
            self.df[f'log_return_next_{period}d'] = returns.shift(-period)
        
        # Trading signals (for classification models)
        # Multi-class signals: -2 (Strong Sell), -1 (Sell), 0 (Hold), 1 (Buy), 2 (Strong Buy)
        future_return_1d = returns.shift(-1)
        future_return_5d = close.pct_change(5).shift(-5)
        
        # 1-day signals
        conditions_1d = [
            future_return_1d <= -0.03,  # Strong sell: >3% loss
            (future_return_1d > -0.03) & (future_return_1d <= -0.01),  # Sell: 1-3% loss
            (future_return_1d > -0.01) & (future_return_1d < 0.01),   # Hold: ±1%
            (future_return_1d >= 0.01) & (future_return_1d < 0.03),   # Buy: 1-3% gain
            future_return_1d >= 0.03   # Strong buy: >3% gain
        ]
        self.df['signal_next_1d'] = np.select(conditions_1d, [-2, -1, 0, 1, 2], default=0)
        
        # 5-day signals
        conditions_5d = [
            future_return_5d <= -0.10,  # Strong sell: >10% loss
            (future_return_5d > -0.10) & (future_return_5d <= -0.05),  # Sell: 5-10% loss
            (future_return_5d > -0.05) & (future_return_5d < 0.05),    # Hold: ±5%
            (future_return_5d >= 0.05) & (future_return_5d < 0.10),    # Buy: 5-10% gain
            future_return_5d >= 0.10   # Strong buy: >10% gain
        ]
        self.df['signal_next_5d'] = np.select(conditions_5d, [-2, -1, 0, 1, 2], default=0)
        
        # Binary signals (simplified)
        self.df['binary_signal_next_1d'] = np.where(future_return_1d > 0.01, 1,
                                                   np.where(future_return_1d < -0.01, -1, 0))
        self.df['binary_signal_next_5d'] = np.where(future_return_5d > 0.05, 1,
                                                   np.where(future_return_5d < -0.05, -1, 0))
        
        # Signal strength (for probability/confidence models)
        self.df['signal_strength_next_1d'] = abs(future_return_1d)
        self.df['signal_strength_next_5d'] = abs(future_return_5d)
        
        # Volatility targets (for volatility models)
        for period in [5, 10, 20]:
            future_vol = self.df['log_return_1d'].rolling(period).std().shift(-period) * np.sqrt(252)
            self.df[f'volatility_target_{period}d'] = future_vol
    
    def _create_model_feature_sets(self):
        """Create model-specific feature sets"""
        print("  - Creating model-specific feature sets...")
        
        # Get all feature columns (exclude targets and basic OHLCV)
        all_features = [col for col in self.df.columns 
                       if not any(x in col for x in ['next_', 'target_', 'signal_']) 
                       and col not in ['open', 'high', 'low', 'close', 'volume']]
        
        # ARCH Model Features
        self.arch_features = [
            'log_return_1d',
            'volatility_20d',
            'return_skewness_10d',
            'vol_extreme_event_flag'
        ]
        
        # GARCH Model Features
        self.garch_features = [
            'log_return_1d',
            'volatility_clustering',
            'vol_of_vol',
            'volatility_ratio',
            'vol_regime_stability',
            'parkinson_vol',
            'vol_percentile_20d',
            'return_autocorr_5d'
        ]
        
        # EGARCH Model Features
        self.egarch_features = [
            'log_return_1d',
            'return_skewness_10d',
            'negative_return_flag',
            'positive_return_flag',
            'range_position',
            'price_vs_ma20',
            'rsi_14',
            'vol_ratio_signal',
            'parkinson_vol',
            'vol_clustering_strength',
            'trend_strength'
        ]
        
        # GJR-GARCH Model Features
        self.gjr_features = [
            'log_return_1d',
            'negative_return_flag',
            'return_skewness_10d',
            'negative_squared_return',
            'positive_squared_return',
            'volatility_regime',
            'price_percentile_30d',
            'ma_bullish_alignment',
            'vol_extreme_event_flag',
            'vol_clustering_strength',
            'parkinson_vol'
        ]
        
        # APARCH Model Features
        self.aparch_features = [
            'log_return_1d',
            'parkinson_vol',
            'garman_klass_vol',
            'return_magnitude',
            'abs_return',
            'return_skewness_10d',
            'negative_return_flag',
            'leverage_proxy',
            'hurst_exponent',
            'vol_of_vol',
            'noise_ratio',
            'vol_regime_stability',
            'volatility_clustering',
            'vol_mean_reversion_signal'
        ]
        
        # Price Prediction Model Features (Top 50)
        self.price_prediction_features = [
            # Core price features
            'log_return_1d', 'price_momentum_5d', 'price_momentum_10d',
            
            # Technical indicators
            'rsi_14', 'rsi_30', 'macd', 'macd_signal', 'macd_histogram',
            'stochastic_k', 'stochastic_d', 'bollinger_position',
            
            # Moving averages
            'price_vs_ma5', 'price_vs_ma20', 'price_vs_ma50', 'price_vs_ma200',
            'ma20_vs_ma50', 'ma50_vs_ma200', 'ma_bullish_alignment',
            
            # Volatility
            'volatility_20d', 'parkinson_vol', 'volatility_ratio',
            'volatility_momentum', 'vol_regime_high',
            
            # Volume
            'volume_ratio_20', 'on_balance_volume', 'volume_momentum_5d',
            'money_flow_index', 'price_vs_vwap',
            
            # Support/Resistance
            'distance_to_pivot', 'resistance_strength_20', 'support_strength_20',
            'breakout_up_20', 'breakout_down_20',
            
            # Position/Momentum
            'price_percentile_30d', 'distance_from_high_30d', 'distance_from_low_30d',
            'momentum_persistence_5d', 'relative_strength',
            
            # Advanced
            'z_score_20d', 'mean_reversion_pressure', 'trend_strength',
            'trend_quality_score', 'hurst_exponent', 'information_ratio',
            
            # Market regime
            'bull_market_flag', 'market_stress_indicator', 'risk_on_score',
            
            # Time features
            'day_of_week_sin', 'month_sin', 'is_month_end'
        ]
        
        # Signal Generation Model Features (Top 40)
        self.signal_prediction_features = [
            # Momentum indicators
            'price_momentum_5d', 'return_momentum_5d', 'macd_histogram',
            'rsi_14', 'stochastic_k', 'williams_r',
            
            # Trend indicators
            'price_vs_ma20', 'ma20_vs_ma50', 'trend_strength',
            'ma_bullish_alignment', 'golden_cross', 'death_cross',
            
            # Volatility regime
            'volatility_ratio', 'vol_regime_transition', 'volatility_momentum',
            'vol_breakout_probability', 'market_stress_indicator',
            
            # Price position
            'price_percentile_30d', 'bollinger_position', 'distance_to_pivot',
            'breakout_up_20', 'breakout_down_20',
            
            # Volume confirmation
            'volume_momentum_5d', 'money_flow_index', 'on_balance_volume',
            'volume_ratio_20',
            
            # Support/Resistance
            'resistance_strength_20', 'support_strength_20',
            
            # Advanced signals
            'z_score_20d', 'mean_reversion_pressure', 'return_skewness_10d',
            'return_persistence', 'trend_persistence',
            
            # Market regime
            'bull_market_flag', 'bear_market_flag', 'risk_on_score',
            
            # Time effects
            'day_of_week', 'is_month_end'
        ]
        
        # Filter features to only include those that exist
        self.arch_features = [f for f in self.arch_features if f in self.df.columns]
        self.garch_features = [f for f in self.garch_features if f in self.df.columns]
        self.egarch_features = [f for f in self.egarch_features if f in self.df.columns]
        self.gjr_features = [f for f in self.gjr_features if f in self.df.columns]
        self.aparch_features = [f for f in self.aparch_features if f in self.df.columns]
        self.price_prediction_features = [f for f in self.price_prediction_features if f in self.df.columns]
        self.signal_prediction_features = [f for f in self.signal_prediction_features if f in self.df.columns]
    
    # Helper methods for calculations
    def _calculate_garman_klass(self):
        """Calculate Garman-Klass volatility estimator"""
        o, h, l, c = self.df['open'], self.df['high'], self.df['low'], self.df['close']
        term1 = 0.5 * (np.log(h/l))**2
        term2 = (2*np.log(2) - 1) * (np.log(c/o))**2
        gk_vol = (term1 - term2).rolling(20).mean()
        return np.sqrt(gk_vol * 252)
    
    def _calculate_vol_clustering(self):
        """Measure volatility clustering/persistence"""
        vol_changes = self.df['volatility_5d'].pct_change().abs()
        return vol_changes.rolling(10).std()
    
    def _calculate_trend_strength(self):
        """Calculate trend strength based on price consistency"""
        close = self.df['close']
        trend_up = (close > close.shift(1)).rolling(10).sum() / 10
        trend_down = (close < close.shift(1)).rolling(10).sum() / 10
        return np.maximum(trend_up, trend_down)
    
    def _calculate_trend_persistence(self):
        """Calculate how long trends persist"""
        price_changes = np.sign(self.df['close'].diff())
        trend_persistence = pd.Series(index=self.df.index, dtype=float)
        window = 20
        
        for i in range(window, len(self.df)):
            recent_changes = price_changes.iloc[i-window:i]
            runs = []
            current_run = 1
            
            for j in range(1, len(recent_changes)):
                if recent_changes.iloc[j] == recent_changes.iloc[j-1]:
                    current_run += 1
                else:
                    runs.append(current_run)
                    current_run = 1
            
            if runs:
                trend_persistence.iloc[i] = np.mean(runs)
        
        return trend_persistence
    
    def _calculate_trend_quality(self):
        """Calculate trend quality score"""
        close = self.df['close']
        returns = self.df['log_return_1d']
        
        # Trend consistency
        trend_direction = np.sign(close.rolling(20).apply(lambda x: x.iloc[-1] - x.iloc[0]))
        daily_consistency = (np.sign(returns) == trend_direction).rolling(20).mean()
        
        # Trend smoothness
        smoothness = 1 - (returns.rolling(20).std() / abs(returns.rolling(20).mean()))
        
        # Combined trend quality
        return (daily_consistency + smoothness.fillna(0)) / 2
    
    def _calculate_noise_ratio(self):
        """Estimate noise vs signal in price movements"""
        returns = self.df['log_return_1d']
        actual_vol = returns.rolling(20).std()
        daily_returns = returns.rolling(20).apply(lambda x: np.mean(np.abs(x)))
        theoretical_vol = daily_returns * np.sqrt(np.pi/2)
        return actual_vol / theoretical_vol
    
    def _calculate_market_efficiency(self):
        """Calculate market efficiency measure"""
        returns = self.df['log_return_1d']
        # Variance ratio test approximation
        var_1 = returns.rolling(20).var()
        var_2 = returns.rolling(40).apply(lambda x: np.var(x.rolling(2).sum().dropna()))
        return var_2 / (2 * var_1)
    
    def _calculate_hurst(self, returns):
        """Calculate Hurst exponent for trend vs mean reversion"""
        if len(returns) < 10:
            return np.nan
        try:
            n = len(returns)
            mean_return = np.mean(returns)
            cum_devs = np.cumsum(returns - mean_return)
            R = np.max(cum_devs) - np.min(cum_devs)
            S = np.std(returns)
            if S == 0:
                return 0.5
            rs_ratio = R / S
            if rs_ratio > 0:
                return np.log(rs_ratio) / np.log(n)
            else:
                return 0.5
        except:
            return np.nan
    
    def _calculate_max_drawdown(self, price_series, window=252):
        """Calculate maximum drawdown"""
        rolling_max = price_series.rolling(window).max()
        drawdown = (price_series - rolling_max) / rolling_max
        return drawdown.rolling(window).min()
    
    def _add_volatility_interpretations(self):
        """Add enhanced volatility interpretation columns"""
        print("    - Adding volatility interpretations...")
        
        # Parkinson Volatility Interpretations
        self.df['parkinson_vol_level'] = self.df['parkinson_vol'].apply(self._interpret_parkinson_level)
        self.df['parkinson_vol_percentile'] = self.df['parkinson_vol'].rolling(252).rank(pct=True) * 100
        self.df['parkinson_vol_regime'] = self.df.apply(lambda row: self._classify_parkinson_regime(
            row['parkinson_vol'], row.get('parkinson_vol_percentile', 50)), axis=1)
        self.df['parkinson_trend_5d'] = self._calculate_volatility_trend(self.df['parkinson_vol'], 5)
        self.df['parkinson_vs_standard_ratio'] = self.df['parkinson_vol'] / self.df['volatility_20d']
        
        # Garman-Klass Interpretations
        self.df['gk_vol_level'] = self.df['garman_klass_vol'].apply(self._interpret_gk_level)
        self.df['gk_vol_percentile'] = self.df['garman_klass_vol'].rolling(252).rank(pct=True) * 100
        self.df['gk_vs_parkinson_ratio'] = self.df['garman_klass_vol'] / self.df['parkinson_vol']
        self.df['gk_vol_quality'] = self.df.apply(lambda row: self._assess_gk_quality(
            row['garman_klass_vol'], row['parkinson_vol']), axis=1)
        self.df['gk_trend_direction'] = self._calculate_volatility_trend(self.df['garman_klass_vol'], 5)
        
        # Volatility Ratio Enhancements
        self.df['vol_ratio_interpretation'] = self.df['volatility_ratio'].apply(self._interpret_vol_ratio)
        self.df['vol_ratio_percentile'] = self.df['volatility_ratio'].rolling(252).rank(pct=True) * 100
        self.df['vol_ratio_regime'] = self.df['volatility_ratio'].apply(self._classify_vol_ratio_regime)
        self.df['vol_ratio_signal'] = self.df['volatility_ratio'].apply(self._generate_vol_ratio_signal)
        self.df['vol_ratio_risk_level'] = self.df['volatility_ratio'].apply(self._assess_vol_ratio_risk)
        
        # Volatility Percentile System
        self.df['vol_percentile_20d'] = self.df['volatility_20d'].rolling(60).rank(pct=True) * 100
        self.df['vol_percentile_60d'] = self.df['volatility_60d'].rolling(252).rank(pct=True) * 100
        self.df['vol_percentile_252d'] = self.df['volatility_20d'].rolling(1000).rank(pct=True) * 100
        self.df['vol_percentile_interpretation'] = self.df.apply(
            lambda row: self._interpret_vol_percentiles(row), axis=1)
        
        # Volatility High/Low Analysis
        self._add_vol_high_low_analysis()
        
        # Advanced Volatility Analysis
        self._add_advanced_vol_analysis()
        
        # Composite Volatility Scores
        self._add_composite_vol_scores()
    
    # Interpretation helper methods
    def _interpret_parkinson_level(self, value):
        if pd.isna(value): return ""
        elif value < 25: return "Very Low"
        elif value < 45: return "Low"
        elif value < 65: return "Moderate"
        elif value < 85: return "High"
        elif value < 120: return "Very High"
        else: return "Extreme"
    
    def _classify_parkinson_regime(self, vol, percentile):
        if pd.isna(vol) or pd.isna(percentile): return ""
        if percentile < 20 and vol < 35: return "Compression"
        elif percentile < 50 and vol < 60: return "Normal"
        elif percentile < 80 and vol < 90: return "Elevated"
        else: return "Crisis"
    
    def _calculate_volatility_trend(self, vol_series, window):
        trend = vol_series.pct_change(window)
        def classify_trend(value):
            if pd.isna(value): return ""
            elif value > 0.3: return "Rising Fast"
            elif value > 0.1: return "Rising"
            elif value > -0.1: return "Stable"
            elif value > -0.3: return "Falling"
            else: return "Falling Fast"
        return trend.apply(classify_trend)
    
    def _interpret_gk_level(self, value):
        if pd.isna(value): return ""
        elif value < 23: return "Very Low"
        elif value < 43: return "Low"
        elif value < 63: return "Moderate"
        elif value < 83: return "High"
        elif value < 115: return "Very High"
        else: return "Extreme"
    
    def _assess_gk_quality(self, gk_vol, park_vol):
        if pd.isna(gk_vol) or pd.isna(park_vol): return ""
        ratio = gk_vol / park_vol
        if 0.9 <= ratio <= 1.1: return "High Quality"
        elif 0.8 <= ratio <= 1.2: return "Good Quality"
        else: return "Low Quality"
    
    def _interpret_vol_ratio(self, value):
        if pd.isna(value): return ""
        elif value < 0.5: return "Extreme Compression"
        elif value < 0.8: return "Compression"
        elif value < 1.2: return "Normal"
        elif value < 1.8: return "Expansion"
        else: return "Extreme Expansion"
    
    def _classify_vol_ratio_regime(self, value):
        if pd.isna(value): return ""
        elif value < 0.6: return "Deep Compression"
        elif value < 0.8: return "Building Compression"
        elif value < 1.2: return "Normal"
        elif value < 1.5: return "Early Expansion"
        elif value < 2.0: return "Full Expansion"
        else: return "Extreme Expansion"
    
    def _generate_vol_ratio_signal(self, value):
        if pd.isna(value): return ""
        elif value < 0.6: return "Strong Breakout Setup"
        elif value < 1.2: return "Neutral"
        elif value < 0.8: return "Breakout Building"
        elif value < 1.8: return "Trend Acceleration"
        else: return "Extreme Conditions"
    
    def _assess_vol_ratio_risk(self, value):
        if pd.isna(value): return ""
        elif value < 0.7: return "Very Low Risk"
        elif value < 1.0: return "Low Risk"
        elif value < 1.3: return "Normal Risk"
        elif value < 1.8: return "High Risk"
        else: return "Extreme Risk"
    
    def _interpret_vol_percentiles(self, row):
        vol_20d_pct = row.get('vol_percentile_20d', 50)
        vol_60d_pct = row.get('vol_percentile_60d', 50)
        if pd.isna(vol_20d_pct): return ""
        avg_percentile = (vol_20d_pct + vol_60d_pct) / 2 if not pd.isna(vol_60d_pct) else vol_20d_pct
        if avg_percentile < 10: return "Bottom Decile"
        elif avg_percentile < 25: return "Low Quartile"
        elif avg_percentile < 50: return "Below Median"
        elif avg_percentile < 75: return "Above Median"
        elif avg_percentile < 90: return "Top Quartile"
        else: return "Top Decile"
    
    def _add_vol_high_low_analysis(self):
        vol_20d = self.df['volatility_20d']
        vol_60d_high = vol_20d.rolling(60).max()
        vol_60d_low = vol_20d.rolling(60).min()
        
        self.df['vol_near_high_flag'] = vol_20d > (vol_60d_high * 0.9)
        self.df['vol_near_low_flag'] = vol_20d < (vol_60d_low * 1.1)
        self.df['vol_high_low_position'] = ((vol_20d - vol_60d_low) / (vol_60d_high - vol_60d_low) * 100).fillna(50)
        
        vol_95th = vol_20d.rolling(252).quantile(0.95)
        vol_5th = vol_20d.rolling(252).quantile(0.05)
        self.df['vol_extreme_event_flag'] = (vol_20d > vol_95th) | (vol_20d < vol_5th)
    
    def _add_advanced_vol_analysis(self):
        vol_20d = self.df['volatility_20d']
        
        vol_high_threshold = vol_20d.rolling(252).quantile(0.75)
        high_vol_periods = (vol_20d > vol_high_threshold).astype(int)
        self.df['vol_clustering_strength'] = high_vol_periods.rolling(10).sum() / 10
        
        vol_252d_mean = vol_20d.rolling(252).mean()
        vol_distance = (vol_20d - vol_252d_mean) / vol_252d_mean
        self.df['vol_mean_reversion_signal'] = vol_distance.apply(self._classify_mean_reversion)
        
        vol_ratio = self.df['volatility_ratio']
        compression_strength = (1 - vol_ratio).clip(0, 1)
        self.df['vol_breakout_probability'] = (compression_strength * 100).round(1)
        
        vol_std = vol_20d.rolling(20).std()
        vol_mean_20d = vol_20d.rolling(20).mean()
        stability_ratio = vol_std / vol_mean_20d
        self.df['vol_regime_stability'] = stability_ratio.apply(self._classify_regime_stability)
        
        vol_momentum = self.df['volatility_momentum']
        self.df['vol_forward_indicator'] = vol_momentum.apply(self._classify_forward_indicator)
    
    def _add_composite_vol_scores(self):
        vol_20d = self.df['volatility_20d']
        vol_ratio = self.df['volatility_ratio']
        vol_momentum = self.df['volatility_momentum']
        
        health_base = (100 - (vol_20d.clip(0, 150) / 150 * 100)).fillna(50)
        regime_adjustment = vol_ratio.apply(lambda x: 10 if 0.8 <= x <= 1.2 else -10 if x > 1.5 else 0)
        self.df['volatility_health_score'] = (health_base + regime_adjustment).clip(0, 100).round(1)
        
        compression_score = (1 - vol_ratio.clip(0, 2) / 2) * 100
        momentum_bonus = vol_momentum.apply(lambda x: 20 if abs(x) > 0.2 else 0)
        self.df['volatility_opportunity_score'] = (compression_score + momentum_bonus).clip(0, 100).round(1)
        
        vol_risk = (vol_20d.clip(0, 150) / 150 * 100).fillna(50)
        expansion_penalty = vol_ratio.apply(lambda x: 30 if x > 1.5 else 0)
        self.df['volatility_risk_score'] = (vol_risk + expansion_penalty).clip(0, 100).round(1)
    
    def _classify_mean_reversion(self, value):
        if pd.isna(value): return ""
        elif abs(value) > 0.5: return "Strong"
        elif abs(value) > 0.25: return "Moderate"
        else: return "Weak"
    
    def _classify_regime_stability(self, value):
        if pd.isna(value): return ""
        elif value < 0.1: return "Very Stable"
        elif value < 0.2: return "Stable"
        elif value < 0.4: return "Transitioning"
        elif value < 0.6: return "Unstable"
        else: return "Chaotic"
    
    def _classify_forward_indicator(self, value):
        if pd.isna(value): return ""
        elif value > 0.1: return "Rising"
        elif value < -0.1: return "Falling"
        else: return "Stable"
    
    def get_model_features(self, model_type):
        """Get features for specific model type"""
        feature_map = {
            'ARCH': self.arch_features,
            'GARCH': self.garch_features,
            'EGARCH': self.egarch_features,
            'GJR-GARCH': self.gjr_features,
            'APARCH': self.aparch_features,
            'Price_Prediction': self.price_prediction_features,
            'Signal_Generation': self.signal_prediction_features
        }
        return feature_map.get(model_type, [])

# Bitcoin Data Fetcher
def fetch_bitcoin_data(start_date='2020-01-01', end_date=None, period='max', debug=False):
    """Fetch Bitcoin data from Yahoo Finance with proper date handling"""
    try:
        import yfinance as yf
        print("Fetching Bitcoin data from Yahoo Finance...")
        
        btc_ticker = "BTC-USD"
        btc = yf.Ticker(btc_ticker)
        
        if start_date and end_date:
            btc_data = btc.history(start=start_date, end=end_date, interval='1d')
        elif start_date:
            btc_data = btc.history(start=start_date, interval='1d')
        else:
            btc_data = btc.history(period=period, interval='1d')
        
        if btc_data.empty:
            raise ValueError("No data retrieved. Check your date range or internet connection.")
        
        if debug:
            print(f"Original data shape: {btc_data.shape}")
            print(f"Date range: {btc_data.index.min()} to {btc_data.index.max()}")
        
        btc_data.columns = btc_data.columns.str.lower()
        btc_data = btc_data[['open', 'high', 'low', 'close', 'volume']].copy()
        
        if btc_data.index.tz is not None:
            print(f"📅 Removing timezone from dates (was: {btc_data.index.tz})")
            btc_data.index = btc_data.index.tz_localize(None)
        
        if not isinstance(btc_data.index, pd.DatetimeIndex):
            btc_data.index = pd.to_datetime(btc_data.index)
        
        btc_data = btc_data.dropna()
        
        print(f"✅ Successfully fetched {len(btc_data)} days of Bitcoin data")
        print(f"📅 Date range: {btc_data.index.min().strftime('%Y-%m-%d')} to {btc_data.index.max().strftime('%Y-%m-%d')}")
        print(f"💰 Price range: ${btc_data['close'].min():.2f} to ${btc_data['close'].max():.2f}")
        
        return btc_data
        
    except ImportError:
        print("❌ yfinance not installed. Installing now...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "yfinance"])
        import yfinance as yf
        return fetch_bitcoin_data(start_date, end_date, period, debug)
        
    except Exception as e:
        print(f"❌ Error fetching Bitcoin data: {e}")
        return None

# Excel Export Functions with Separate Sheets for Each Model
def save_to_excel_with_model_sheets(featured_data, feature_engine, filename=None):
    """Save Bitcoin data with separate sheets for each model type"""
    try:
        if filename is None:
            current_date = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            filename = f'bitcoin_complete_models_{current_date}.xlsx'
        
        print(f"💾 Saving Bitcoin data with model-specific sheets: {filename}")
        
        # Prepare the main data
        export_data = featured_data.copy()
        
        if export_data.index.tz is not None:
            export_data.index = export_data.index.tz_localize(None)
        
        export_data = export_data.reset_index()
        if 'index' in export_data.columns:
            export_data.rename(columns={'index': 'Date'}, inplace=True)
        
        if 'Date' in export_data.columns:
            export_data['Date'] = pd.to_datetime(export_data['Date'])
            print(f"✅ Date column preserved: {export_data['Date'].dtype}")
        
        # Add interpretation columns
        export_data = add_interpretation_columns(export_data)
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            
            # 1. Complete Dataset
            export_data.to_excel(writer, sheet_name='Complete_Dataset', index=False)
            print("✅ Complete dataset saved to 'Complete_Dataset' sheet")
            
            # 2. ARCH Model Sheet
            arch_features = feature_engine.get_model_features('ARCH')
            if arch_features:
                arch_data = export_data[['Date'] + arch_features + ['volatility_target_5d', 'volatility_target_10d']].copy()
                arch_data.to_excel(writer, sheet_name='ARCH_Model', index=False)
                print("✅ ARCH model data saved")
            
            # 3. GARCH Model Sheet
            garch_features = feature_engine.get_model_features('GARCH')
            if garch_features:
                garch_data = export_data[['Date'] + garch_features + ['volatility_target_5d', 'volatility_target_10d']].copy()
                garch_data.to_excel(writer, sheet_name='GARCH_Model', index=False)
                print("✅ GARCH model data saved")
            
            # 4. EGARCH Model Sheet
            egarch_features = feature_engine.get_model_features('EGARCH')
            if egarch_features:
                egarch_data = export_data[['Date'] + egarch_features + ['volatility_target_5d', 'volatility_target_10d']].copy()
                egarch_data.to_excel(writer, sheet_name='EGARCH_Model', index=False)
                print("✅ EGARCH model data saved")
            
            # 5. GJR-GARCH Model Sheet
            gjr_features = feature_engine.get_model_features('GJR-GARCH')
            if gjr_features:
                gjr_data = export_data[['Date'] + gjr_features + ['volatility_target_5d', 'volatility_target_10d']].copy()
                gjr_data.to_excel(writer, sheet_name='GJR_GARCH_Model', index=False)
                print("✅ GJR-GARCH model data saved")
            
            # 6. APARCH Model Sheet
            aparch_features = feature_engine.get_model_features('APARCH')
            if aparch_features:
                aparch_data = export_data[['Date'] + aparch_features + ['volatility_target_5d', 'volatility_target_10d']].copy()
                aparch_data.to_excel(writer, sheet_name='APARCH_Model', index=False)
                print("✅ APARCH model data saved")
            
            # 7. Price Prediction Model Sheet
            price_features = feature_engine.get_model_features('Price_Prediction')
            if price_features:
                price_targets = ['close_next_1d', 'close_next_3d', 'close_next_5d', 'return_next_1d', 'return_next_5d']
                available_targets = [col for col in price_targets if col in export_data.columns]
                price_data = export_data[['Date'] + price_features + available_targets].copy()
                price_data.to_excel(writer, sheet_name='Price_Prediction_Model', index=False)
                print("✅ Price prediction model data saved")
            
            # 8. Signal Generation Model Sheet
            signal_features = feature_engine.get_model_features('Signal_Generation')
            if signal_features:
                signal_targets = ['signal_next_1d', 'signal_next_5d', 'binary_signal_next_1d', 'signal_strength_next_1d']
                available_targets = [col for col in signal_targets if col in export_data.columns]
                signal_data = export_data[['Date'] + signal_features + available_targets].copy()
                signal_data.to_excel(writer, sheet_name='Signal_Generation_Model', index=False)
                print("✅ Signal generation model data saved")
            
            # 9. Technical Indicators Sheet
            technical_features = [col for col in export_data.columns if any(x in col.lower() for x in 
                                ['rsi', 'macd', 'stochastic', 'bollinger', 'williams', 'cci', 'roc'])]
            if technical_features:
                tech_data = export_data[['Date'] + technical_features].copy()
                tech_data.to_excel(writer, sheet_name='Technical_Indicators', index=False)
                print("✅ Technical indicators saved")
            
            # 10. Volatility Analysis Sheet
            vol_features = [col for col in export_data.columns if 'volatility' in col.lower() or 'vol_' in col.lower()]
            if vol_features:
                vol_data = export_data[['Date'] + vol_features].copy()
                vol_data.to_excel(writer, sheet_name='Volatility_Analysis', index=False)
                print("✅ Volatility analysis saved")
            
            # 11. Market Regime Sheet
            regime_features = [col for col in export_data.columns if any(x in col.lower() for x in 
                             ['regime', 'bull_market', 'bear_market', 'risk_on', 'market_stress'])]
            if regime_features:
                regime_data = export_data[['Date'] + regime_features].copy()
                regime_data.to_excel(writer, sheet_name='Market_Regime', index=False)
                print("✅ Market regime data saved")
            
            # 12. Feature Summary Sheet
            feature_summary = create_feature_summary(export_data)
            feature_summary.to_excel(writer, sheet_name='Feature_Summary', index=True)
            print("✅ Feature summary saved")
            
            # Format all worksheets
            for sheet_name in writer.sheets:
                worksheet = writer.sheets[sheet_name]
                if sheet_name != 'Feature_Summary':
                    format_excel_worksheet(worksheet, export_data if sheet_name == 'Complete_Dataset' 
                                         else eval(f"{sheet_name.lower().replace('_model', '').replace('_', '_')}_data") 
                                         if sheet_name.endswith('_Model') else export_data)
        
        print(f"\n🎉 Successfully saved Bitcoin data to: {filename}")
        print(f"📊 Total rows: {len(export_data)}")
        print(f"📈 Total columns: {len(export_data.columns)}")
        print(f"📅 Date range: {export_data['Date'].min().date()} to {export_data['Date'].max().date()}")
        print(f"\n📋 Sheets created:")
        print(f"  1. Complete_Dataset - All features and data")
        print(f"  2. ARCH_Model - Features for ARCH volatility model")
        print(f"  3. GARCH_Model - Features for GARCH volatility model")
        print(f"  4. EGARCH_Model - Features for EGARCH volatility model")
        print(f"  5. GJR_GARCH_Model - Features for GJR-GARCH volatility model")
        print(f"  6. APARCH_Model - Features for APARCH volatility model")
        print(f"  7. Price_Prediction_Model - Features for price prediction")
        print(f"  8. Signal_Generation_Model - Features for trading signals")
        print(f"  9. Technical_Indicators - All technical indicators")
        print(f"  10. Volatility_Analysis - All volatility features")
        print(f"  11. Market_Regime - Market regime classification")
        print(f"  12. Feature_Summary - Statistical summary of all features")
        
        return filename
        
    except Exception as e:
        print(f"❌ Error saving to Excel: {e}")
        return None

def add_interpretation_columns(df):
    """Add interpretation columns for key features"""
    
    def interpret_skewness(value):
        if pd.isna(value): return ""
        elif value < -0.5: return "Crash Risk"
        elif value > 0.5: return "Upside Bias"
        else: return "Balanced"
    
    def interpret_persistence(value):
        if pd.isna(value): return ""
        elif value > 0.8: return "Strong Trend"
        elif value < 0.6: return "Choppy"
        else: return "Moderate Trend"
    
    def interpret_vol_regime(value):
        if pd.isna(value): return ""
        elif value == 0: return "Low Vol"
        elif value == 1: return "Medium Vol"
        else: return "High Vol"
    
    def interpret_price_vs_ma(value):
        if pd.isna(value): return ""
        elif value > 5: return "Strong Uptrend"
        elif value > 0: return "Uptrend"
        elif value > -5: return "Downtrend"
        else: return "Strong Downtrend"
    
    def interpret_rsi(value):
        if pd.isna(value): return ""
        elif value > 70: return "Overbought"
        elif value < 30: return "Oversold"
        else: return "Neutral"
    
    def interpret_signal(value):
        if pd.isna(value): return ""
        elif value == 2: return "Strong Buy"
        elif value == 1: return "Buy"
        elif value == 0: return "Hold"
        elif value == -1: return "Sell"
        else: return "Strong Sell"
    
    # Add interpretation columns
    col_positions = {col: i for i, col in enumerate(df.columns)}
    
    if 'return_skewness_10d' in df.columns:
        pos = col_positions['return_skewness_10d'] + 1
        df.insert(pos, 'skewness_interpretation', 
                 df['return_skewness_10d'].apply(interpret_skewness))
    
    if 'return_persistence' in df.columns:
        pos = col_positions.get('skewness_interpretation', col_positions['return_persistence']) + 1
        df.insert(pos, 'persistence_interpretation', 
                 df['return_persistence'].apply(interpret_persistence))
    
    if 'volatility_regime' in df.columns:
        pos = col_positions.get('persistence_interpretation', col_positions['volatility_regime']) + 1
        df.insert(pos, 'vol_regime_interpretation', 
                 df['volatility_regime'].apply(interpret_vol_regime))
    
    if 'price_vs_ma20' in df.columns:
        pos = col_positions.get('vol_regime_interpretation', col_positions['price_vs_ma20']) + 1
        df.insert(pos, 'trend_interpretation', 
                 df['price_vs_ma20'].apply(interpret_price_vs_ma))
    
    if 'rsi_14' in df.columns:
        pos = col_positions.get('trend_interpretation', col_positions['rsi_14']) + 1
        df.insert(pos, 'rsi_interpretation', 
                 df['rsi_14'].apply(interpret_rsi))
    
    if 'signal_next_1d' in df.columns:
        pos = col_positions.get('rsi_interpretation', col_positions['signal_next_1d']) + 1
        df.insert(pos, 'signal_interpretation', 
                 df['signal_next_1d'].apply(interpret_signal))
    
    return df

def format_excel_worksheet(worksheet, df):
    """Format the Excel worksheet for better readability"""
    try:
        from openpyxl.styles import Font, PatternFill, Alignment
        from openpyxl.utils import get_column_letter
        
        # Header formatting
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        
        for col in range(1, len(df.columns) + 1):
            cell = worksheet.cell(row=1, column=col)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = Alignment(horizontal="center")
        
        # Date column formatting
        if 'Date' in df.columns:
            date_col_idx = df.columns.get_loc('Date') + 1
            date_col_letter = get_column_letter(date_col_idx)
            for row in range(2, min(len(df) + 2, 1000)):  # Limit for performance
                cell = worksheet[f"{date_col_letter}{row}"]
                cell.number_format = 'YYYY-MM-DD'
        
        # Price columns formatting
        price_cols = ['open', 'high', 'low', 'close']
        for col_name in price_cols:
            if col_name in df.columns:
                col_idx = df.columns.get_loc(col_name) + 1
                col_letter = get_column_letter(col_idx)
                for row in range(2, min(len(df) + 2, 1000)):
                    cell = worksheet[f"{col_letter}{row}"]
                    cell.number_format = '#,##0.00'
        
        # Volume formatting
        if 'volume' in df.columns:
            vol_col_idx = df.columns.get_loc('volume') + 1
            vol_col_letter = get_column_letter(vol_col_idx)
            for row in range(2, min(len(df) + 2, 1000)):
                cell = worksheet[f"{vol_col_letter}{row}"]
                cell.number_format = '#,##0'
        
        # Auto-adjust column widths (limited for performance)
        for col_idx, column in enumerate(worksheet.columns):
            if col_idx > 50:  # Limit to first 50 columns for performance
                break
            max_length = 0
            column_letter = get_column_letter(col_idx + 1)
            
            # Check first 10 rows for width
            for row_idx in range(min(10, len(df) + 1)):
                cell = worksheet.cell(row=row_idx + 1, column=col_idx + 1)
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 25)
            worksheet.column_dimensions[column_letter].width = adjusted_width
        
        # Freeze the header row
        worksheet.freeze_panes = "A2"
        
    except ImportError:
        print("⚠️ openpyxl formatting not available - basic export completed")
    except Exception as e:
        print(f"⚠️ Excel formatting error: {e} - basic export completed")

def create_feature_summary(df):
    """Create feature summary statistics"""
    feature_cols = [col for col in df.columns 
                   if col not in ['Date'] and not any(x in col for x in ['interpretation', '_next_', 'target_'])]
    
    summary = {}
    for col in feature_cols[:100]:  # Limit for performance
        try:
            if df[col].dtype in ['object', 'bool']:
                summary[col] = {
                    'Type': 'Categorical',
                    'Unique_Values': df[col].nunique(),
                    'Most_Common': df[col].mode().iloc[0] if len(df[col].mode()) > 0 else 'N/A',
                    'Missing_Count': df[col].isna().sum(),
                    'Missing_Pct': f"{df[col].isna().sum() / len(df) * 100:.1f}%"
                }
            else:
                summary[col] = {
                    'Type': 'Numeric',
                    'Mean': df[col].mean(),
                    'Std': df[col].std(),
                    'Min': df[col].min(),
                    'Max': df[col].max(),
                    'Missing_Count': df[col].isna().sum(),
                    'Missing_Pct': f"{df[col].isna().sum() / len(df) * 100:.1f}%"
                }
        except:
            summary[col] = {'Type': 'Error', 'Description': 'Could not analyze'}
    
    return pd.DataFrame(summary).T

# Main workflow functions
def download_bitcoin_complete_models(start_date='2020-01-01', end_date=None, filename=None):
    """Complete workflow: Fetch Bitcoin data, calculate all features, save with model sheets"""
    print("🚀 Starting Complete Bitcoin Models Data Pipeline...")
    print("="*80)
    
    # Step 1: Fetch Bitcoin data
    print("📊 Step 1: Fetching Bitcoin data from Yahoo Finance...")
    btc_data = fetch_bitcoin_data(start_date=start_date, end_date=end_date)
    
    if btc_data is None or btc_data.empty:
        print("❌ Failed to fetch Bitcoin data")
        return None, None
    
    # Step 2: Calculate all features
    print("\n🔧 Step 2: Calculating comprehensive feature set...")
    try:
        feature_engine = EnhancedBitcoinFeatureEngine(btc_data)
        featured_data = feature_engine.calculate_all_features()
        
        if featured_data.index.tz is not None:
            featured_data.index = featured_data.index.tz_localize(None)
            
    except Exception as e:
        print(f"❌ Error calculating features: {e}")
        return None, None
    
    # Step 3: Save to Excel with model sheets
    print(f"\n💾 Step 3: Saving to Excel with model-specific sheets...")
    saved_filename = save_to_excel_with_model_sheets(featured_data, feature_engine, filename=filename)
    
    if saved_filename:
        print(f"\n✅ COMPLETE! Bitcoin data saved to: {saved_filename}")
        
        # Show comprehensive summary
        print(f"\n📋 COMPREHENSIVE SUMMARY:")
        print(f"📅 Data Period: {featured_data.index.min().date()} to {featured_data.index.max().date()}")
        print(f"📊 Total Records: {len(featured_data):,}")
        print(f"🔢 Total Features: {len(featured_data.columns)}")
        print(f"💰 Current Price: ${featured_data['close'].iloc[-1]:,.2f}")
        
        if len(featured_data) >= 30:
            print(f"📈 30-day Return: {((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%")
        
        # Model feature counts
        print(f"\n🎯 MODEL FEATURE COUNTS:")
        print(f"  📊 ARCH Model: {len(feature_engine.get_model_features('ARCH'))} features")
        print(f"  📊 GARCH Model: {len(feature_engine.get_model_features('GARCH'))} features")
        print(f"  📊 EGARCH Model: {len(feature_engine.get_model_features('EGARCH'))} features")
        print(f"  📊 GJR-GARCH Model: {len(feature_engine.get_model_features('GJR-GARCH'))} features")
        print(f"  📊 APARCH Model: {len(feature_engine.get_model_features('APARCH'))} features")
        print(f"  📊 Price Prediction: {len(feature_engine.get_model_features('Price_Prediction'))} features")
        print(f"  📊 Signal Generation: {len(feature_engine.get_model_features('Signal_Generation'))} features")
        
        return featured_data, saved_filename
    else:
        return featured_data, None

# Quick download functions for different periods
def download_bitcoin_models_1year(filename=None):
    """Download last 1 year of Bitcoin data with all models"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=1)
    return download_bitcoin_complete_models(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_models_1year.xlsx'
    )

def download_bitcoin_models_2years(filename=None):
    """Download last 2 years of Bitcoin data with all models"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=2)
    return download_bitcoin_complete_models(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_models_2years.xlsx'
    )

def download_bitcoin_models_5years(filename=None):
    """Download last 5 years of Bitcoin data with all models"""
    end_date = pd.Timestamp.now()
    start_date = end_date - pd.DateOffset(years=5)
    return download_bitcoin_complete_models(
        start_date=start_date.strftime('%Y-%m-%d'),
        filename=filename or 'bitcoin_models_5years.xlsx'
    )

def download_bitcoin_models_max(filename=None):
    """Download maximum available Bitcoin data with all models"""
    return download_bitcoin_complete_models(
        start_date='2010-01-01',
        filename=filename or 'bitcoin_models_max.xlsx'
    )

if __name__ == "__main__":
    # Example usage - Download Bitcoin data with all model sheets
    print("🚀 Enhanced Bitcoin Prediction System")
    print("=" * 60)
    print("Features:")
    print("  • Complete volatility model features (ARCH, GARCH, EGARCH, GJR-GARCH, APARCH)")
    print("  • Price prediction features (150+ technical indicators)")
    print("  • Signal generation features (trading signals)")
    print("  • Separate Excel sheets for each model type")
    print("  • Professional formatting and interpretations")
    print("=" * 60)
    
    # Download last 2 years with all models
    print("\n📊 Downloading last 2 years of Bitcoin data with all models...")
    data, filename = download_bitcoin_models_2years()
    
    # Alternative options:
    # data, filename = download_bitcoin_models_1year()
    # data, filename = download_bitcoin_models_5years()
    # data, filename = download_bitcoin_models_max()
    
    # Custom date range:
    # data, filename = download_bitcoin_complete_models(
    #     start_date='2023-01-01', 
    #     end_date='2024-12-31',
    #     filename='bitcoin_custom_models.xlsx'
    # )
    
    if filename:
        print(f"\n🎉 SUCCESS! Your complete Bitcoin dataset is ready!")
        print(f"📄 File: {filename}")
        print(f"\n📋 What you got:")
        print(f"  ✅ 12 separate Excel sheets for different models")
        print(f"  ✅ 200+ engineered features")
        print(f"  ✅ Volatility model features (ARCH, GARCH, EGARCH, GJR-GARCH, APARCH)")
        print(f"  ✅ Price prediction features")
        print(f"  ✅ Trading signal generation features")
        print(f"  ✅ Technical indicators and market regime analysis")
        print(f"  ✅ Professional Excel formatting")
        print(f"  ✅ Human-readable interpretations")
        print(f"\n🎯 Ready for:")
        print(f"  • Statistical volatility modeling")
        print(f"  • Machine learning price prediction")
        print(f"  • Trading signal generation")
        print(f"  • Market regime analysis")
        print(f"  • Risk management modeling")
    else:
        print("\n❌ Download failed!")



# Fix the error with undefined model data variables
def save_to_excel_with_model_sheets(featured_data, feature_engine, filename=None):
    """Save Bitcoin data with separate sheets for each model type"""
    try:
        if filename is None:
            current_date = pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')
            filename = f'bitcoin_complete_models_{current_date}.xlsx'
        
        print(f"💾 Saving Bitcoin data with model-specific sheets: {filename}")
        
        # Prepare the main data
        export_data = featured_data.copy()
        
        if export_data.index.tz is not None:
            export_data.index = export_data.index.tz_localize(None)
        
        export_data = export_data.reset_index()
        if 'index' in export_data.columns:
            export_data.rename(columns={'index': 'Date'}, inplace=True)
        
        if 'Date' in export_data.columns:
            export_data['Date'] = pd.to_datetime(export_data['Date'])
            print(f"✅ Date column preserved: {export_data['Date'].dtype}")
        
        # Add interpretation columns
        export_data = add_interpretation_columns(export_data)
        
        with pd.ExcelWriter(filename, engine='openpyxl') as writer:
            
            # 1. Complete Dataset
            export_data.to_excel(writer, sheet_name='Complete_Dataset', index=False)
            print("✅ Complete dataset saved to 'Complete_Dataset' sheet")
            
            # Get available feature sets based on what exists in the data
            # ARCH Model Sheet
            model_sheets = {}
            
            model_types = [
                'ARCH', 'GARCH', 'EGARCH', 'GJR-GARCH', 'APARCH', 
                'Price_Prediction', 'Signal_Generation'
            ]
            
            sheet_names = [
                'ARCH_Model', 'GARCH_Model', 'EGARCH_Model', 'GJR_GARCH_Model',
                'APARCH_Model', 'Price_Prediction_Model', 'Signal_Generation_Model'
            ]
            
            # Create model-specific dataframes
            for model_type, sheet_name in zip(model_types, sheet_names):
                try:
                    features = feature_engine.get_model_features(model_type)
                    
                    if features:
                        # Add Date column and model-specific features
                        cols = ['Date'] + features
                        
                        # Add target columns if they exist
                        if model_type in ['ARCH', 'GARCH', 'EGARCH', 'GJR-GARCH', 'APARCH']:
                            target_cols = ['volatility_target_5d', 'volatility_target_10d']
                            cols += [col for col in target_cols if col in export_data.columns]
                        elif model_type == 'Price_Prediction':
                            target_cols = ['close_next_1d', 'close_next_3d', 'close_next_5d', 
                                         'return_next_1d', 'return_next_5d']
                            cols += [col for col in target_cols if col in export_data.columns]
                        elif model_type == 'Signal_Generation':
                            target_cols = ['signal_next_1d', 'signal_next_5d', 
                                         'binary_signal_next_1d', 'signal_strength_next_1d']
                            cols += [col for col in target_cols if col in export_data.columns]
                        
                        # Filter to only include columns that exist in export_data
                        available_cols = [col for col in cols if col in export_data.columns]
                        
                        if available_cols:
                            model_data = export_data[available_cols].copy()
                            model_sheets[sheet_name] = model_data
                            model_data.to_excel(writer, sheet_name=sheet_name, index=False)
                            print(f"✅ {model_type} model data saved to '{sheet_name}' sheet")
                except Exception as e:
                    print(f"⚠️ Could not save {model_type} model data: {e}")
            
            # 9. Technical Indicators Sheet
            technical_features = [col for col in export_data.columns if any(x in col.lower() for x in 
                                ['rsi', 'macd', 'stochastic', 'bollinger', 'williams', 'cci', 'roc'])]
            if technical_features:
                tech_data = export_data[['Date'] + technical_features].copy()
                tech_data.to_excel(writer, sheet_name='Technical_Indicators', index=False)
                model_sheets['Technical_Indicators'] = tech_data
                print("✅ Technical indicators saved")
            
            # 10. Volatility Analysis Sheet
            vol_features = [col for col in export_data.columns if 'volatility' in col.lower() or 'vol_' in col.lower()]
            if vol_features:
                vol_data = export_data[['Date'] + vol_features].copy()
                vol_data.to_excel(writer, sheet_name='Volatility_Analysis', index=False)
                model_sheets['Volatility_Analysis'] = vol_data
                print("✅ Volatility analysis saved")
            
            # 11. Market Regime Sheet
            regime_features = [col for col in export_data.columns if any(x in col.lower() for x in 
                             ['regime', 'bull_market', 'bear_market', 'risk_on', 'market_stress'])]
            if regime_features:
                regime_data = export_data[['Date'] + regime_features].copy()
                regime_data.to_excel(writer, sheet_name='Market_Regime', index=False)
                model_sheets['Market_Regime'] = regime_data
                print("✅ Market regime data saved")
            
            # 12. Feature Summary Sheet
            feature_summary = create_feature_summary(export_data)
            feature_summary.to_excel(writer, sheet_name='Feature_Summary', index=True)
            print("✅ Feature summary saved")
            
            # Format all worksheets
            for sheet_name in writer.sheets:
                worksheet = writer.sheets[sheet_name]
                if sheet_name != 'Feature_Summary':
                    if sheet_name in model_sheets:
                        format_excel_worksheet(worksheet, model_sheets[sheet_name])
                    else:
                        format_excel_worksheet(worksheet, export_data)
        
        print(f"\n🎉 Successfully saved Bitcoin data to: {filename}")
        print(f"📊 Total rows: {len(export_data)}")
        print(f"📈 Total columns: {len(export_data.columns)}")
        print(f"📅 Date range: {export_data['Date'].min().date()} to {export_data['Date'].max().date()}")
        print(f"\n📋 Sheets created:")
        print(f"  1. Complete_Dataset - All features and data")
        print(f"  2. ARCH_Model - Features for ARCH volatility model")
        print(f"  3. GARCH_Model - Features for GARCH volatility model")
        print(f"  4. EGARCH_Model - Features for EGARCH volatility model")
        print(f"  5. GJR_GARCH_Model - Features for GJR-GARCH volatility model")
        print(f"  6. APARCH_Model - Features for APARCH volatility model")
        print(f"  7. Price_Prediction_Model - Features for price prediction")
        print(f"  8. Signal_Generation_Model - Features for trading signals")
        print(f"  9. Technical_Indicators - All technical indicators")
        print(f"  10. Volatility_Analysis - All volatility features")
        print(f"  11. Market_Regime - Market regime classification")
        print(f"  12. Feature_Summary - Statistical summary of all features")
        
        return filename
        
    except Exception as e:
        print(f"❌ Error saving to Excel: {e}")
        return None

# Download Bitcoin data with all model features
print("🚀 Starting Bitcoin Data Download & Feature Engineering...")
print("="*60)

# Step 1: Fetch Bitcoin data (last 2 years)
end_date = pd.Timestamp.now()
start_date = end_date - pd.DateOffset(years=2)
print(f"📊 Step 1: Fetching Bitcoin data from {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}...")

btc_data = fetch_bitcoin_data(start_date=start_date.strftime('%Y-%m-%d'))

if btc_data is None or btc_data.empty:
    print("❌ Failed to fetch Bitcoin data")
else:
    # Step 2: Calculate features
    print("\n🔧 Step 2: Calculating all price-based features...")
    try:
        feature_engine = EnhancedBitcoinFeatureEngine(btc_data)
        featured_data = feature_engine.calculate_all_features()
        
        # Ensure no timezone issues
        if featured_data.index.tz is not None:
            featured_data.index = featured_data.index.tz_localize(None)
            
        # Step 3: Save to Excel with model sheets
        print(f"\n💾 Step 3: Saving to Excel with model-specific sheets...")
        saved_filename = save_to_excel_with_model_sheets(featured_data, feature_engine, filename="bitcoin_models_2years12.xlsx")
        
        if saved_filename:
            print(f"\n✅ COMPLETE! Bitcoin data saved to: {saved_filename}")
            
            # Show comprehensive summary
            print(f"\n📋 COMPREHENSIVE SUMMARY:")
            print(f"📅 Data Period: {featured_data.index.min().date()} to {featured_data.index.max().date()}")
            print(f"📊 Total Records: {len(featured_data):,}")
            print(f"🔢 Total Features: {len(featured_data.columns)}")
            print(f"💰 Current Price: ${featured_data['close'].iloc[-1]:,.2f}")
            
            if len(featured_data) >= 30:
                print(f"📈 30-day Return: {((featured_data['close'].iloc[-1] / featured_data['close'].iloc[-30]) - 1) * 100:.2f}%")
            
            # Model feature counts
            print(f"\n🎯 MODEL FEATURE COUNTS:")
            print(f"  📊 ARCH Model: {len(feature_engine.get_model_features('ARCH'))} features")
            print(f"  📊 GARCH Model: {len(feature_engine.get_model_features('GARCH'))} features")
            print(f"  📊 EGARCH Model: {len(feature_engine.get_model_features('EGARCH'))} features")
            print(f"  📊 GJR-GARCH Model: {len(feature_engine.get_model_features('GJR-GARCH'))} features")
            print(f"  📊 APARCH Model: {len(feature_engine.get_model_features('APARCH'))} features")
            print(f"  📊 Price Prediction: {len(feature_engine.get_model_features('Price_Prediction'))} features")
            print(f"  📊 Signal Generation: {len(feature_engine.get_model_features('Signal_Generation'))} features")
        else:
            print("\n❌ Save to Excel failed!")
            
    except Exception as e:
        print(f"❌ Error calculating features: {e}")
        import traceback
        traceback.print_exc()

